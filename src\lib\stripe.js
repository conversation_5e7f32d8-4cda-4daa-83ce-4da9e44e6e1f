import { loadStripe } from '@stripe/stripe-js'

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY)

// Pricing configuration
export const PRICING_PLANS = {
  starter: {
    id: 'starter',
    name: 'Starter Pack',
    price: 19,
    credits: 40,
    priceId: 'price_starter_pack', // Replace with actual Stripe Price ID
    features: [
      '40 headshots (all styles)',
      'All style categories',
      'No watermarks',
      '30-day retention',
      'Priority processing',
      'Email support'
    ]
  },
  pro: {
    id: 'pro',
    name: 'Pro Pack',
    price: 35,
    credits: 100,
    priceId: 'price_pro_pack', // Replace with actual Stripe Price ID
    features: [
      '100 headshots (all styles)',
      'Advanced customization',
      'Bulk download (ZIP)',
      '90-day retention',
      'API access (future)',
      'Priority support (24h)'
    ]
  }
}

// Create checkout session
export const createCheckoutSession = async (planId, userId) => {
  try {
    const plan = PRICING_PLANS[planId]
    if (!plan) {
      throw new Error('Invalid plan selected')
    }

    const response = await fetch('/api/payments/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        priceId: plan.priceId,
        userId,
        planId,
        successUrl: `${window.location.origin}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${window.location.origin}/pricing`
      }),
    })

    const { sessionId } = await response.json()
    
    const stripe = await stripePromise
    const { error } = await stripe.redirectToCheckout({ sessionId })
    
    if (error) {
      throw error
    }

  } catch (error) {
    console.error('Checkout error:', error)
    throw error
  }
}

// Verify payment session
export const verifyPaymentSession = async (sessionId) => {
  try {
    const response = await fetch(`/api/payments/verify-session/${sessionId}`)
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.error || 'Payment verification failed')
    }
    
    return data
  } catch (error) {
    console.error('Payment verification error:', error)
    throw error
  }
}

// Get payment history
export const getPaymentHistory = async (userId) => {
  try {
    const response = await fetch(`/api/payments/history/${userId}`)
    const data = await response.json()
    
    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch payment history')
    }
    
    return data.payments
  } catch (error) {
    console.error('Payment history error:', error)
    throw error
  }
}
