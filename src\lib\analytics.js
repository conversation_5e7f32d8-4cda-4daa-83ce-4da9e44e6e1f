// Google Analytics 4 Integration for HeadGenius Pro

// Initialize Google Analytics
export const initGA = () => {
  const GA_ID = import.meta.env.VITE_GOOGLE_ANALYTICS_ID
  
  if (!GA_ID) {
    console.warn('Google Analytics ID not found')
    return
  }

  // Load gtag script
  const script1 = document.createElement('script')
  script1.async = true
  script1.src = `https://www.googletagmanager.com/gtag/js?id=${GA_ID}`
  document.head.appendChild(script1)

  // Initialize gtag
  window.dataLayer = window.dataLayer || []
  function gtag() {
    window.dataLayer.push(arguments)
  }
  window.gtag = gtag

  gtag('js', new Date())
  gtag('config', GA_ID, {
    page_title: document.title,
    page_location: window.location.href
  })
}

// Track page views
export const trackPageView = (path, title) => {
  if (typeof window.gtag === 'function') {
    window.gtag('config', import.meta.env.VITE_GOOGLE_ANALYTICS_ID, {
      page_path: path,
      page_title: title
    })
  }
}

// Track custom events
export const trackEvent = (action, category, label, value) => {
  if (typeof window.gtag === 'function') {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value
    })
  }
}

// Track user signup
export const trackSignup = (method = 'email') => {
  trackEvent('sign_up', 'engagement', method)
}

// Track headshot generation
export const trackGeneration = (styleId, tier) => {
  trackEvent('generate_headshot', 'product', styleId, 1)
  trackEvent('generation_by_tier', 'product', tier, 1)
}

// Track purchases
export const trackPurchase = (planId, value, currency = 'USD') => {
  if (typeof window.gtag === 'function') {
    window.gtag('event', 'purchase', {
      transaction_id: Date.now().toString(),
      value: value,
      currency: currency,
      items: [{
        item_id: planId,
        item_name: planId === 'starter' ? 'Starter Pack' : 'Pro Pack',
        category: 'credits',
        quantity: 1,
        price: value
      }]
    })
  }
}

// Track file uploads
export const trackUpload = (fileSize, fileType) => {
  trackEvent('file_upload', 'engagement', fileType, Math.round(fileSize / 1024)) // Size in KB
}

// Track style selection
export const trackStyleSelection = (styleId, category) => {
  trackEvent('style_selected', 'product', styleId)
  trackEvent('category_selected', 'product', category)
}

// Track pricing page views
export const trackPricingView = () => {
  trackEvent('view_pricing', 'engagement', 'pricing_page')
}

// Track gallery interactions
export const trackGalleryView = (category) => {
  trackEvent('view_gallery', 'engagement', category)
}

// Track support interactions
export const trackSupportContact = (method) => {
  trackEvent('contact_support', 'engagement', method)
}

// Track errors
export const trackError = (errorType, errorMessage) => {
  trackEvent('error', 'technical', errorType)
}

// Track user engagement time
export const trackEngagementTime = (timeSpent, page) => {
  trackEvent('engagement_time', 'behavior', page, Math.round(timeSpent / 1000)) // Time in seconds
}

// Enhanced ecommerce tracking
export const trackBeginCheckout = (planId, value) => {
  if (typeof window.gtag === 'function') {
    window.gtag('event', 'begin_checkout', {
      currency: 'USD',
      value: value,
      items: [{
        item_id: planId,
        item_name: planId === 'starter' ? 'Starter Pack' : 'Pro Pack',
        category: 'credits',
        quantity: 1,
        price: value
      }]
    })
  }
}

// Track user properties
export const setUserProperties = (userId, tier) => {
  if (typeof window.gtag === 'function') {
    window.gtag('config', import.meta.env.VITE_GOOGLE_ANALYTICS_ID, {
      user_id: userId,
      custom_map: {
        subscription_tier: tier
      }
    })
  }
}

// Track conversion goals
export const trackConversion = (goalName, value = 1) => {
  trackEvent('conversion', 'goal', goalName, value)
}

// Utility function to track time on page
export const createPageTimer = () => {
  const startTime = Date.now()
  
  return {
    stop: (pageName) => {
      const timeSpent = Date.now() - startTime
      trackEngagementTime(timeSpent, pageName)
    }
  }
}
