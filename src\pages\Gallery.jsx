import { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Camera, Download, Share2, Star, ArrowRight, Zap, Sparkles } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const galleryRef = useRef(null)
  const beforeAfterRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.gallery-title',
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )

      gsap.fromTo('.gallery-subtitle',
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating sparkles
      gsap.to('.floating-sparkle', {
        y: -30,
        x: 20,
        rotation: 360,
        duration: 4,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.6
      })

      // Before/After cards animation
      gsap.fromTo('.before-after-card',
        { y: 80, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: beforeAfterRef.current,
            start: 'top 80%'
          }
        }
      )

      // Arrow pulse animation
      gsap.to('.arrow-pulse', {
        scale: 1.1,
        duration: 1,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1
      })

      // Transformation effect on hover
      gsap.set('.transform-arrow', { scale: 0 })
    }, galleryRef)

    return () => ctx.revert()
  }, [])

  // Enhanced data with before/after images
  const examples = [
    {
      id: 1,
      style: 'Professional Corporate',
      category: 'professional',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Perfect for LinkedIn and corporate websites'
    },
    {
      id: 2,
      style: 'Executive Portrait',
      category: 'professional',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Commanding presence for leadership roles'
    },
    {
      id: 3,
      style: 'Modern Business',
      category: 'professional',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Contemporary look for modern professionals'
    },
    {
      id: 4,
      style: 'Library Setting',
      category: 'professional',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.7,
      description: 'Intellectual and approachable atmosphere'
    },

    {
      id: 5,
      style: 'Golden Hour',
      category: 'casual',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Warm golden hour lighting'
    },
    {
      id: 6,
      style: 'Coffee Shop',
      category: 'casual',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Cozy coffee shop atmosphere'
    },
    {
      id: 7,
      style: 'Beach Lifestyle',
      category: 'casual',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.7,
      description: 'Relaxed beach vibes'
    },
    {
      id: 8,
      style: 'Park Setting',
      category: 'casual',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Natural park environment'
    },

    {
      id: 9,
      style: 'Romantic',
      category: 'dating',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Romantic and charming'
    },
    {
      id: 10,
      style: 'Fun & Playful',
      category: 'dating',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Playful and energetic'
    },
    {
      id: 11,
      style: 'Elegant',
      category: 'dating',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Sophisticated elegance'
    },
    {
      id: 12,
      style: 'Adventurous',
      category: 'dating',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.7,
      description: 'Adventure-ready spirit'
    },

    {
      id: 13,
      style: 'Artistic',
      category: 'creative',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Creative artistic expression'
    },
    {
      id: 14,
      style: 'Urban Edge',
      category: 'creative',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Modern urban aesthetic'
    },
    {
      id: 15,
      style: 'Studio Pro',
      category: 'creative',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.7,
      description: 'Professional studio quality'
    },
    {
      id: 16,
      style: 'Vintage Film',
      category: 'creative',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Classic vintage film look'
    },
    {
      id: 17,
      style: 'Minimalist',
      category: 'creative',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Clean minimalist design'
    },

    {
      id: 18,
      style: 'Commercial Actor',
      category: 'actor',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Perfect for commercial casting'
    },
    {
      id: 19,
      style: 'Dramatic Actor',
      category: 'actor',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Intense dramatic presence'
    },
    {
      id: 20,
      style: 'Fashion Model',
      category: 'actor',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.7,
      description: 'High-fashion modeling'
    },
    {
      id: 21,
      style: 'Commercial Model',
      category: 'actor',
      beforeImage: '/api/placeholder/300/300',
      afterImage: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'Versatile commercial appeal'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Styles', icon: '🎯' },
    { id: 'professional', name: 'Professional', icon: '💼' },
    { id: 'casual', name: 'Casual', icon: '🌿' },
    { id: 'dating', name: 'Dating', icon: '💖' },
    { id: 'creative', name: 'Creative', icon: '🎨' },
    { id: 'actor', name: 'Actor & Model', icon: '🎭' }
  ]

  return (
    <div className="min-h-screen gradient-bg py-8" ref={galleryRef}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <section className="py-12 relative overflow-hidden">
          <div className="container mx-auto px-4 text-center relative z-10">
            {/* Floating sparkles */}
            <div className="floating-sparkle absolute top-10 left-10 text-primary-400 opacity-30">
              <Sparkles className="w-12 h-12" />
            </div>
            <div className="floating-sparkle absolute top-20 right-20 text-accent-400 opacity-25">
              <Zap className="w-10 h-10" />
            </div>
            <div className="floating-sparkle absolute bottom-20 left-20 text-primary-400 opacity-20">
              <Camera className="w-14 h-14" />
            </div>

            <h1 className="gallery-title text-5xl md:text-6xl font-bold text-secondary-900 mb-6">
              Before & <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">After</span> Gallery
            </h1>
            <p className="gallery-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
              See the incredible transformations powered by our AI technology. Real photos, stunning results.
            </p>
          </div>
        </section>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-full border transition-all duration-200 ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white border-primary-600'
                  : 'bg-white border-secondary-200 hover:border-primary-300 hover:bg-primary-50 text-secondary-700'
              }`}
            >
              <span>{category.icon}</span>
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </motion.div>

        {/* Before/After Gallery Grid */}
        <div className="space-y-8 mb-12" ref={beforeAfterRef}>
          {examples
            .filter(example => selectedCategory === 'all' || example.category === selectedCategory)
            .map((example, index) => (
            <motion.div
              key={example.id}
              className="before-after-card group"
              whileHover={{ y: -5 }}
            >
              <div className="card overflow-hidden">
                {/* Before/After Container */}
                <div className="relative p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
                    {/* Before Image */}
                    <div className="relative">
                      <div className="aspect-square bg-secondary-100 rounded-lg overflow-hidden group-hover:shadow-lg transition-all duration-300">
                        <div className="w-full h-full flex items-center justify-center">
                          <Camera className="w-12 h-12 text-secondary-400" />
                        </div>
                      </div>
                      <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-secondary-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                          Before
                        </span>
                      </div>
                    </div>

                    {/* Cool Arrow */}
                    <div className="flex justify-center">
                      <div className="relative">
                        <motion.div
                          className="arrow-pulse w-16 h-16 bg-gradient-to-r from-primary-600 to-accent-600 rounded-full flex items-center justify-center shadow-lg"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <ArrowRight className="w-8 h-8 text-white" />
                        </motion.div>

                        {/* Sparkle effects */}
                        <motion.div
                          className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full"
                          animate={{
                            scale: [1, 1.5, 1],
                            opacity: [1, 0.5, 1]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                        <motion.div
                          className="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-400 rounded-full"
                          animate={{
                            scale: [1, 1.3, 1],
                            opacity: [1, 0.7, 1]
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: 0.5
                          }}
                        />
                      </div>
                    </div>

                    {/* After Image */}
                    <div className="relative">
                      <div className="aspect-square bg-gradient-to-br from-primary-50 to-accent-50 rounded-lg overflow-hidden group-hover:shadow-xl transition-all duration-300 border-2 border-primary-200">
                        <div className="w-full h-full flex items-center justify-center">
                          <Sparkles className="w-12 h-12 text-primary-500" />
                        </div>
                      </div>
                      <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-gradient-to-r from-primary-600 to-accent-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                          After ✨
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="mt-8 text-center">
                    <div className="flex items-center justify-center gap-3 mb-3">
                      <h3 className="text-2xl font-bold text-secondary-900 group-hover:text-primary-600 transition-colors duration-200">
                        {example.style}
                      </h3>
                      <div className="flex items-center gap-1">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium text-secondary-600">{example.rating}</span>
                      </div>
                    </div>

                    <p className="text-secondary-600 mb-6 text-lg leading-relaxed max-w-md mx-auto">
                      {example.description}
                    </p>

                    <div className="flex gap-4 justify-center">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="btn-primary inline-flex items-center gap-2"
                      >
                        <Zap className="w-5 h-5" />
                        Try This Style
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="btn-secondary inline-flex items-center gap-2"
                      >
                        <Share2 className="w-4 h-4" />
                        Share
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200 text-center"
        >
          <h2 className="text-2xl font-bold text-secondary-900 mb-4">
            Ready to Create Your Own?
          </h2>
          <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
            Join thousands of professionals who've transformed their image with HeadGenius Pro. 
            Start with our free tier or upgrade for unlimited possibilities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary inline-flex items-center gap-2">
              <Camera className="w-5 h-5" />
              Generate Free Headshot
            </button>
            <button className="btn-secondary">
              View Pricing Plans
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Gallery
