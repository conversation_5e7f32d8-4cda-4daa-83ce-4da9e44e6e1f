import { useState } from 'react'
import { motion } from 'framer-motion'
import { Camera, Download, Share2, Star } from 'lucide-react'

const Gallery = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  // Placeholder data - will be replaced with real data from Supabase
  const examples = [
    { id: 1, style: 'Professional Corporate', category: 'professional', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 2, style: 'Executive Portrait', category: 'professional', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 3, style: 'Modern Business', category: 'professional', image: '/api/placeholder/300/300', rating: 4.8 },
    { id: 4, style: 'Library Setting', category: 'professional', image: '/api/placeholder/300/300', rating: 4.7 },

    { id: 5, style: 'Golden Hour', category: 'casual', image: '/api/placeholder/300/300', rating: 4.8 },
    { id: 6, style: 'Coffee Shop', category: 'casual', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 7, style: 'Beach Lifestyle', category: 'casual', image: '/api/placeholder/300/300', rating: 4.7 },
    { id: 8, style: 'Park Setting', category: 'casual', image: '/api/placeholder/300/300', rating: 4.8 },

    { id: 9, style: 'Romantic', category: 'dating', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 10, style: 'Fun & Playful', category: 'dating', image: '/api/placeholder/300/300', rating: 4.8 },
    { id: 11, style: 'Elegant', category: 'dating', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 12, style: 'Adventurous', category: 'dating', image: '/api/placeholder/300/300', rating: 4.7 },

    { id: 13, style: 'Artistic', category: 'creative', image: '/api/placeholder/300/300', rating: 4.8 },
    { id: 14, style: 'Urban Edge', category: 'creative', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 15, style: 'Studio Pro', category: 'creative', image: '/api/placeholder/300/300', rating: 4.7 },
    { id: 16, style: 'Vintage Film', category: 'creative', image: '/api/placeholder/300/300', rating: 4.8 },
    { id: 17, style: 'Minimalist', category: 'creative', image: '/api/placeholder/300/300', rating: 4.9 },

    { id: 18, style: 'Commercial Actor', category: 'actor', image: '/api/placeholder/300/300', rating: 4.8 },
    { id: 19, style: 'Dramatic Actor', category: 'actor', image: '/api/placeholder/300/300', rating: 4.9 },
    { id: 20, style: 'Fashion Model', category: 'actor', image: '/api/placeholder/300/300', rating: 4.7 },
    { id: 21, style: 'Commercial Model', category: 'actor', image: '/api/placeholder/300/300', rating: 4.8 }
  ]

  const categories = [
    { id: 'all', name: 'All Styles', icon: '🎯' },
    { id: 'professional', name: 'Professional', icon: '💼' },
    { id: 'casual', name: 'Casual', icon: '🌿' },
    { id: 'dating', name: 'Dating', icon: '💖' },
    { id: 'creative', name: 'Creative', icon: '🎨' },
    { id: 'actor', name: 'Actor & Model', icon: '🎭' }
  ]

  return (
    <div className="min-h-screen gradient-bg py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-secondary-900 mb-4">
            Headshot Gallery
          </h1>
          <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
            Explore amazing headshots created with HeadGenius Pro. Get inspired and see what's possible.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-full border transition-all duration-200 ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white border-primary-600'
                  : 'bg-white border-secondary-200 hover:border-primary-300 hover:bg-primary-50 text-secondary-700'
              }`}
            >
              <span>{category.icon}</span>
              <span className="font-medium">{category.name}</span>
            </button>
          ))}
        </motion.div>

        {/* Gallery Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {examples
            .filter(example => selectedCategory === 'all' || example.category === selectedCategory)
            .map((example, index) => (
            <motion.div
              key={example.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="card group hover:shadow-lg transition-all duration-300"
            >
              <div className="relative overflow-hidden rounded-lg mb-4">
                <div className="aspect-square bg-secondary-100 flex items-center justify-center">
                  <Camera className="w-12 h-12 text-secondary-400" />
                </div>
                
                {/* Overlay on hover */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4">
                  <button className="p-2 bg-white rounded-full hover:bg-secondary-100 transition-colors duration-200">
                    <Download className="w-5 h-5 text-secondary-700" />
                  </button>
                  <button className="p-2 bg-white rounded-full hover:bg-secondary-100 transition-colors duration-200">
                    <Share2 className="w-5 h-5 text-secondary-700" />
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-secondary-900 mb-1">
                    {example.style}
                  </h3>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-secondary-600">{example.rating}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200 text-center"
        >
          <h2 className="text-2xl font-bold text-secondary-900 mb-4">
            Ready to Create Your Own?
          </h2>
          <p className="text-secondary-600 mb-6 max-w-2xl mx-auto">
            Join thousands of professionals who've transformed their image with HeadGenius Pro. 
            Start with our free tier or upgrade for unlimited possibilities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary inline-flex items-center gap-2">
              <Camera className="w-5 h-5" />
              Generate Free Headshot
            </button>
            <button className="btn-secondary">
              View Pricing Plans
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Gallery
