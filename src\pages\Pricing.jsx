import { useState } from 'react'
import { motion } from 'framer-motion'
import { Check, Sparkles, Crown, Zap } from 'lucide-react'
import { useAuthStore } from '../store/authStore'
import { createCheckoutSession, PRICING_PLANS } from '../lib/stripe'
import toast from 'react-hot-toast'

const Pricing = () => {
  const [loading, setLoading] = useState(null)
  const { user, credits } = useAuthStore()

  const handlePurchase = async (planId) => {
    if (!user) {
      toast.error('Please sign in to purchase credits')
      return
    }

    setLoading(planId)
    try {
      await createCheckoutSession(planId, user.id)
    } catch (error) {
      toast.error('Failed to start checkout process')
      console.error('Checkout error:', error)
    } finally {
      setLoading(null)
    }
  }

  const plans = [
    {
      id: 'free',
      name: 'Free Trial',
      price: '$0',
      period: 'lifetime',
      description: '5 headshots to try HeadGenius',
      icon: <Sparkles className="w-6 h-6" />,
      features: [
        '5 headshots total (lifetime)',
        'Watermarked previews',
        '24-hour file retention',
        'Professional styles only',
        'Then upgrade to continue'
      ],
      cta: 'Current Plan',
      popular: false,
      disabled: true
    },
    {
      id: 'starter',
      name: 'Starter Pack',
      price: '$19',
      period: 'one-time',
      description: '40 professional headshots',
      icon: <Zap className="w-6 h-6" />,
      features: [
        '40 headshots (all styles)',
        'All style categories',
        'No watermarks',
        '30-day retention',
        'Priority processing',
        'Email support'
      ],
      cta: 'Get Started',
      popular: true,
      disabled: false
    },
    {
      id: 'pro',
      name: 'Pro Pack',
      price: '$35',
      period: 'one-time',
      description: '100 headshots with premium features',
      icon: <Crown className="w-6 h-6" />,
      features: [
        '100 headshots (all styles)',
        'Advanced customization',
        'Bulk download (ZIP)',
        '90-day retention',
        'API access (future)',
        'Priority support (24h)'
      ],
      cta: 'Go Pro',
      popular: false,
      disabled: false
    }
  ]

  return (
    <div className="min-h-screen gradient-bg py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-5xl font-bold text-secondary-900 mb-4">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
            Pay once, use forever. No subscriptions, no hidden fees.
            Try 5 free headshots, then upgrade to Pro for unlimited professional headshots.
          </p>
        </motion.div>

        {/* Current Credits Display */}
        {user && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="card max-w-md mx-auto mb-12 text-center"
          >
            <h3 className="text-lg font-semibold text-secondary-900 mb-2">
              Your Current Credits
            </h3>
            <div className="text-3xl font-bold text-primary-600 mb-2">
              {credits.remaining}
            </div>
            <p className="text-secondary-600">
              {credits.tier === 'free' ? 'Free Tier' : `${credits.tier} Plan`}
            </p>
          </motion.div>
        )}

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`card relative ${
                plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''
              } ${plan.disabled ? 'opacity-75' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4 text-primary-600">
                  {plan.icon}
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                  {plan.name}
                </h3>
                <div className="mb-2">
                  <span className="text-4xl font-bold text-secondary-900">{plan.price}</span>
                  <span className="text-secondary-500 ml-1">/{plan.period}</span>
                </div>
                <p className="text-secondary-600">{plan.description}</p>
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-secondary-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={() => !plan.disabled && handlePurchase(plan.id)}
                disabled={plan.disabled || loading === plan.id}
                className={`w-full text-center py-3 px-6 rounded-lg font-medium transition-colors duration-200 ${
                  plan.popular && !plan.disabled
                    ? 'bg-primary-600 hover:bg-primary-700 text-white'
                    : plan.disabled
                    ? 'bg-secondary-200 text-secondary-500 cursor-not-allowed'
                    : 'bg-secondary-100 hover:bg-secondary-200 text-secondary-900'
                } ${loading === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {loading === plan.id ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    Processing...
                  </div>
                ) : (
                  plan.cta
                )}
              </button>
            </motion.div>
          ))}
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="max-w-3xl mx-auto"
        >
          <h2 className="text-3xl font-bold text-center text-secondary-900 mb-8">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-6">
            {[
              {
                question: "Do credits expire?",
                answer: "No! Your credits never expire. Use them whenever you want, at your own pace."
              },
              {
                question: "Can I upgrade my plan later?",
                answer: "Absolutely! You can purchase additional credit packs anytime. All credits stack together."
              },
              {
                question: "What's included in the free trial?",
                answer: "You get 5 lifetime free headshots with watermarked previews. Perfect for trying out the service before upgrading!"
              },
              {
                question: "How long does generation take?",
                answer: "Most headshots are generated in 30-60 seconds. Pro users get priority processing for even faster results."
              },
              {
                question: "Can I get a refund?",
                answer: "Yes! We offer a 30-day money-back guarantee if you're not satisfied with your headshots."
              }
            ].map((faq, index) => (
              <div key={index} className="card">
                <h3 className="font-semibold text-secondary-900 mb-2">
                  {faq.question}
                </h3>
                <p className="text-secondary-600">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Pricing
