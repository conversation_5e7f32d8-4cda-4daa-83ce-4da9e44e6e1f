-- HeadGenius Pro: Complete Database Setup
-- Run this SINGLE script to set up everything correctly
-- This combines schema.sql and security-fixes.sql in the proper order

-- =============================================
-- STEP 1: BASIC SETUP
-- =============================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- STEP 2: CREATE TABLES
-- =============================================

-- User Credits Table
CREATE TABLE IF NOT EXISTS user_credits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    total_credits INTEGER DEFAULT 5, -- New users get 5 lifetime free credits
    used_credits INTEGER DEFAULT 0,
    free_credits_used INTEGER DEFAULT 0, -- Lifetime free credits used (max 5, no resets)
    last_free_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- Kept for backward compatibility
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'starter', 'pro')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Generations Table
CREATE TABLE IF NOT EXISTS generations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    style_id TEXT NOT NULL,
    style_name TEXT,
    input_image_url TEXT NOT NULL,
    output_image_url TEXT,
    replicate_prediction_id TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    cost DECIMAL(10,4) DEFAULT 0,
    gender TEXT DEFAULT 'auto',
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Payments Table
CREATE TABLE IF NOT EXISTS payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_payment_intent_id TEXT UNIQUE,
    stripe_session_id TEXT,
    amount INTEGER NOT NULL, -- Amount in cents
    currency TEXT DEFAULT 'usd',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'succeeded', 'failed', 'canceled')),
    credits_purchased INTEGER DEFAULT 0,
    tier_purchased TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Profiles Table (extended user info)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free',
    total_generations INTEGER DEFAULT 0,
    last_generation_at TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Style Analytics Table (track popular styles)
CREATE TABLE IF NOT EXISTS style_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    style_id TEXT NOT NULL,
    generation_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    avg_rating DECIMAL(3,2) DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(style_id)
);

-- Contact Submissions Table (store contact form submissions)
CREATE TABLE IF NOT EXISTS contact_submissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- Optional, for logged-in users
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    assigned_to TEXT, -- Support team member
    response TEXT, -- Admin response
    responded_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STEP 3: CREATE INDEXES
-- =============================================

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_generations_user_id ON generations(user_id);
CREATE INDEX IF NOT EXISTS idx_generations_created_at ON generations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_generations_status ON generations(status);
CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON user_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent_id ON payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_email ON contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at DESC);

-- =============================================
-- STEP 4: CREATE FUNCTIONS (WITH SECURITY FIXES)
-- =============================================

-- Create updated_at trigger function (SECURE VERSION)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Function to initialize user data on signup (SECURE VERSION)
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (user_id, full_name)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');

    -- Give new users 5 lifetime free credits (no resets)
    INSERT INTO user_credits (user_id, total_credits)
    VALUES (NEW.id, 5);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Function to update style analytics (SECURE VERSION)
CREATE OR REPLACE FUNCTION update_style_analytics(style_id_param TEXT)
RETURNS VOID AS $$
BEGIN
    INSERT INTO style_analytics (style_id, generation_count, last_used_at)
    VALUES (style_id_param, 1, NOW())
    ON CONFLICT (style_id)
    DO UPDATE SET
        generation_count = style_analytics.generation_count + 1,
        last_used_at = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Function to get retention period (SECURE VERSION)
CREATE OR REPLACE FUNCTION get_retention_period(user_tier TEXT)
RETURNS INTERVAL AS $$
BEGIN
    CASE user_tier
        WHEN 'free' THEN RETURN INTERVAL '24 hours';
        WHEN 'starter' THEN RETURN INTERVAL '30 days';
        WHEN 'pro' THEN RETURN INTERVAL '90 days';
        ELSE RETURN INTERVAL '24 hours'; -- Default to free tier
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Function to get storage usage (SECURE VERSION)
CREATE OR REPLACE FUNCTION get_user_storage_usage(user_uuid UUID)
RETURNS TABLE(
    total_files INTEGER,
    total_size_bytes BIGINT,
    input_files INTEGER,
    output_files INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_files,
        COALESCE(SUM(metadata->>'size')::BIGINT, 0) as total_size_bytes,
        COUNT(*) FILTER (WHERE bucket_id = 'user-uploads')::INTEGER as input_files,
        COUNT(*) FILTER (WHERE bucket_id = 'generated-headshots')::INTEGER as output_files
    FROM storage.objects
    WHERE user_uuid::text = (storage.foldername(name))[1];
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Function to check storage quota (SECURE VERSION)
CREATE OR REPLACE FUNCTION check_storage_quota(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_tier TEXT;
    current_usage BIGINT;
    quota_limit BIGINT;
BEGIN
    -- Get user tier
    SELECT subscription_tier INTO user_tier
    FROM user_credits
    WHERE user_id = user_uuid;

    -- Set quota limits (in bytes)
    CASE user_tier
        WHEN 'free' THEN quota_limit := 100 * 1024 * 1024; -- 100MB
        WHEN 'starter' THEN quota_limit := 500 * 1024 * 1024; -- 500MB
        WHEN 'pro' THEN quota_limit := 2 * 1024 * 1024 * 1024; -- 2GB
        ELSE quota_limit := 100 * 1024 * 1024; -- Default 100MB
    END CASE;

    -- Get current usage
    SELECT COALESCE(SUM(metadata->>'size')::BIGINT, 0) INTO current_usage
    FROM storage.objects
    WHERE user_uuid::text = (storage.foldername(name))[1];

    RETURN current_usage < quota_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- =============================================
-- STEP 5: CREATE TRIGGERS
-- =============================================

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_user_credits_updated_at ON user_credits;
CREATE TRIGGER update_user_credits_updated_at BEFORE UPDATE ON user_credits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payments_updated_at ON payments;
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_style_analytics_updated_at ON style_analytics;
CREATE TRIGGER update_style_analytics_updated_at BEFORE UPDATE ON style_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_contact_submissions_updated_at ON contact_submissions;
CREATE TRIGGER update_contact_submissions_updated_at BEFORE UPDATE ON contact_submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to automatically create user data on signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- =============================================
-- STEP 6: ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all tables
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE style_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- =============================================
-- STEP 7: CREATE OPTIMIZED RLS POLICIES
-- =============================================

-- Drop any existing policies first
DROP POLICY IF EXISTS "Users can view own credits" ON user_credits;
DROP POLICY IF EXISTS "Users can update own credits" ON user_credits;
DROP POLICY IF EXISTS "Users can insert own credits" ON user_credits;
DROP POLICY IF EXISTS "Users can view own generations" ON generations;
DROP POLICY IF EXISTS "Users can insert own generations" ON generations;
DROP POLICY IF EXISTS "Users can update own generations" ON generations;
DROP POLICY IF EXISTS "Users can view own payments" ON payments;
DROP POLICY IF EXISTS "Users can insert own payments" ON payments;
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;
DROP POLICY IF EXISTS "Anyone can view style analytics" ON style_analytics;
DROP POLICY IF EXISTS "Anyone can submit contact forms" ON contact_submissions;
DROP POLICY IF EXISTS "Users can view own submissions" ON contact_submissions;

-- OPTIMIZED User Credits Policies (using select auth.uid() for performance)
CREATE POLICY "Users can view own credits" ON user_credits FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own credits" ON user_credits FOR UPDATE USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own credits" ON user_credits FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- OPTIMIZED Generations Policies
CREATE POLICY "Users can view own generations" ON generations FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own generations" ON generations FOR INSERT WITH CHECK ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own generations" ON generations FOR UPDATE USING ((select auth.uid()) = user_id);

-- OPTIMIZED Payments Policies
CREATE POLICY "Users can view own payments" ON payments FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own payments" ON payments FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- OPTIMIZED User Profiles Policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Style Analytics is public read
CREATE POLICY "Anyone can view style analytics" ON style_analytics FOR SELECT USING (true);

-- OPTIMIZED Contact Submissions Policies
CREATE POLICY "Anyone can submit contact forms" ON contact_submissions FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can view own submissions" ON contact_submissions FOR SELECT USING (
    (select auth.uid()) = user_id OR user_id IS NULL
);

-- =============================================
-- STEP 8: CREATE STORAGE BUCKETS & POLICIES
-- =============================================

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public)
VALUES ('user-uploads', 'user-uploads', false)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('generated-headshots', 'generated-headshots', false)
ON CONFLICT (id) DO NOTHING;

-- User uploads policies
DROP POLICY IF EXISTS "Users can upload their own images" ON storage.objects;
CREATE POLICY "Users can upload their own images" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'user-uploads'
    AND (select auth.uid())::text = (storage.foldername(name))[1]
);

DROP POLICY IF EXISTS "Users can view their own uploads" ON storage.objects;
CREATE POLICY "Users can view their own uploads" ON storage.objects
FOR SELECT USING (
    bucket_id = 'user-uploads'
    AND (select auth.uid())::text = (storage.foldername(name))[1]
);

DROP POLICY IF EXISTS "Users can delete their own uploads" ON storage.objects;
CREATE POLICY "Users can delete their own uploads" ON storage.objects
FOR DELETE USING (
    bucket_id = 'user-uploads'
    AND (select auth.uid())::text = (storage.foldername(name))[1]
);

-- Generated headshots policies
DROP POLICY IF EXISTS "Users can view their own generated images" ON storage.objects;
CREATE POLICY "Users can view their own generated images" ON storage.objects
FOR SELECT USING (
    bucket_id = 'generated-headshots'
    AND (select auth.uid())::text = (storage.foldername(name))[1]
);

DROP POLICY IF EXISTS "System can insert generated images" ON storage.objects;
CREATE POLICY "System can insert generated images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'generated-headshots');

DROP POLICY IF EXISTS "System can delete expired images" ON storage.objects;
CREATE POLICY "System can delete expired images" ON storage.objects
FOR DELETE USING (bucket_id = 'generated-headshots');

-- =============================================
-- SETUP COMPLETE!
-- =============================================

-- Verify setup
SELECT 'Database setup completed successfully!' as status;
