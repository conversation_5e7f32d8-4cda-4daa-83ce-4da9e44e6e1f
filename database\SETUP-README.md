# 🚀 HeadGenius Pro: Database Setup Guide

## 🎯 **Simple One-Step Setup**

Instead of running multiple SQL files, just run **ONE** file that does everything correctly:

### **Option 1: Complete Setup (Recommended)**
```sql
-- Run this SINGLE file in Supabase SQL Editor:
database/complete-setup.sql
```

This file includes:
- ✅ All tables with correct structure
- ✅ All security fixes applied
- ✅ Optimized RLS policies
- ✅ Contact form integration
- ✅ New users get 5 credits
- ✅ All performance optimizations

### **Option 2: Step-by-Step (If you prefer)**
```sql
-- 1. First run:
database/schema.sql

-- 2. Then run:
database/security-fixes.sql
```

## 🔧 **What Gets Created**

### **Tables:**
- `user_credits` - User credit management (new users get 5 credits)
- `generations` - AI generation history
- `payments` - Stripe payment records
- `user_profiles` - Extended user information
- `style_analytics` - Popular style tracking
- `contact_submissions` - Contact form submissions

### **Functions (All Secure):**
- `handle_new_user()` - Auto-creates user data on signup
- `update_style_analytics()` - Tracks style usage
- `get_user_storage_usage()` - Storage quota management
- `check_storage_quota()` - Storage limit checking
- `get_retention_period()` - File retention logic

### **Security Features:**
- ✅ Row Level Security enabled on all tables
- ✅ Optimized RLS policies for performance
- ✅ Secure functions with proper search_path
- ✅ Storage bucket policies
- ✅ Contact form security

## 🎉 **After Running the Script**

You should see:
- ✅ **0 security warnings**
- ✅ **0 performance warnings**
- ✅ **0 linter errors**
- ✅ Message: "Database setup completed successfully!"

## 🧪 **Testing Your Setup**

1. **Create a new user account**
   - Should automatically get 5 credits
   - User profile should be created

2. **Test the dashboard**
   - Should show real credit data
   - Storage usage should display correctly

3. **Test contact form**
   - Should save submissions to database
   - No errors in console

4. **Run Supabase Database Linter**
   - Should show no warnings or errors

## 🆘 **If You Get Errors**

### **"relation does not exist" errors:**
- Make sure you deleted ALL existing tables first
- Run `complete-setup.sql` instead of individual files

### **"policy already exists" errors:**
- The script handles this automatically with `DROP POLICY IF EXISTS`
- Safe to ignore or re-run the script

### **Permission errors:**
- Make sure you're running as database owner
- Check you have SUPERUSER privileges

## 📞 **Need Help?**

If you encounter any issues:
1. Check the Supabase logs for detailed error messages
2. Make sure you cleared all existing tables first
3. Try running `complete-setup.sql` instead of separate files

---

**🎯 Recommendation**: Use `complete-setup.sql` for the cleanest, error-free setup!
