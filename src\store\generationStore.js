import { create } from 'zustand'
import { generateHeadshot, uploadImage, getGenerationStatus } from '../lib/api'
import { getStyleConfig } from '../lib/replicate'

export const useGenerationStore = create((set, get) => ({
  // State
  currentGeneration: null,
  generationHistory: [],
  isGenerating: false,
  uploadProgress: 0,
  generationProgress: 0,
  error: null,

  // Actions
  startGeneration: async (file, styleId, userId, gender = 'auto') => {
    set({ 
      isGenerating: true, 
      uploadProgress: 0, 
      generationProgress: 0, 
      error: null 
    })

    try {
      // Step 1: Upload image
      set({ uploadProgress: 25 })
      const uploadResult = await uploadImage(file, userId)
      
      if (!uploadResult.success) {
        throw new Error('Failed to upload image')
      }

      // Step 2: Start generation
      set({ uploadProgress: 50, generationProgress: 10 })
      const generationResult = await generateHeadshot(
        styleId, 
        uploadResult.imageUrl, 
        userId, 
        gender
      )

      if (!generationResult.success) {
        throw new Error('Failed to start generation')
      }

      // Step 3: Monitor progress (simulated for now)
      set({ uploadProgress: 100, generationProgress: 30 })
      
      // Simulate generation progress
      const progressInterval = setInterval(() => {
        const currentProgress = get().generationProgress
        if (currentProgress < 90) {
          set({ generationProgress: currentProgress + 10 })
        }
      }, 1000)

      // Wait for completion (in real implementation, this would poll the status)
      await new Promise(resolve => setTimeout(resolve, 5000))
      clearInterval(progressInterval)

      set({ generationProgress: 100 })

      // Step 4: Complete generation
      const generation = {
        id: generationResult.generation.id,
        styleId,
        styleName: getStyleConfig(styleId)?.description || styleId,
        inputImageUrl: uploadResult.imageUrl,
        outputImageUrl: generationResult.generation.imageUrl,
        cost: generationResult.generation.cost,
        createdAt: generationResult.generation.createdAt,
        status: 'completed'
      }

      set(state => ({
        currentGeneration: generation,
        generationHistory: [generation, ...state.generationHistory],
        isGenerating: false,
        uploadProgress: 0,
        generationProgress: 0
      }))

      return { success: true, generation }

    } catch (error) {
      console.error('Generation error:', error)
      set({ 
        isGenerating: false, 
        error: error.message,
        uploadProgress: 0,
        generationProgress: 0
      })
      return { success: false, error: error.message }
    }
  },

  // Check generation status (for async generations)
  checkGenerationStatus: async (predictionId) => {
    try {
      const result = await getGenerationStatus(predictionId)
      
      if (result.success) {
        const { status, output, error } = result
        
        if (status === 'succeeded' && output) {
          // Update the generation with the final result
          set(state => ({
            generationHistory: state.generationHistory.map(gen => 
              gen.predictionId === predictionId 
                ? { ...gen, status: 'completed', outputImageUrl: output }
                : gen
            )
          }))
        } else if (status === 'failed') {
          set(state => ({
            generationHistory: state.generationHistory.map(gen => 
              gen.predictionId === predictionId 
                ? { ...gen, status: 'failed', error }
                : gen
            )
          }))
        }
      }

      return result
    } catch (error) {
      console.error('Status check error:', error)
      return { success: false, error: error.message }
    }
  },

  // Clear current generation
  clearCurrentGeneration: () => {
    set({ currentGeneration: null })
  },

  // Clear error
  clearError: () => {
    set({ error: null })
  },

  // Reset generation state
  resetGeneration: () => {
    set({
      currentGeneration: null,
      isGenerating: false,
      uploadProgress: 0,
      generationProgress: 0,
      error: null
    })
  },

  // Get generation by ID
  getGenerationById: (id) => {
    const { generationHistory } = get()
    return generationHistory.find(gen => gen.id === id)
  },

  // Delete generation
  deleteGeneration: (id) => {
    set(state => ({
      generationHistory: state.generationHistory.filter(gen => gen.id !== id),
      currentGeneration: state.currentGeneration?.id === id ? null : state.currentGeneration
    }))
  },

  // Get generations by style
  getGenerationsByStyle: (styleId) => {
    const { generationHistory } = get()
    return generationHistory.filter(gen => gen.styleId === styleId)
  },

  // Get recent generations
  getRecentGenerations: (limit = 10) => {
    const { generationHistory } = get()
    return generationHistory
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, limit)
  }
}))

// Utility functions
export const getGenerationStatusText = (status) => {
  switch (status) {
    case 'pending':
      return 'Waiting to start...'
    case 'starting':
      return 'Initializing...'
    case 'processing':
      return 'Generating your headshot...'
    case 'completed':
      return 'Complete!'
    case 'failed':
      return 'Generation failed'
    default:
      return 'Unknown status'
  }
}

export const getGenerationStatusColor = (status) => {
  switch (status) {
    case 'pending':
    case 'starting':
      return 'text-yellow-600'
    case 'processing':
      return 'text-blue-600'
    case 'completed':
      return 'text-green-600'
    case 'failed':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}
