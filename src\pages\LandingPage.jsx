import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Camera, Sparkles, Users, Star, ArrowRight, Check } from 'lucide-react'

const LandingPage = () => {
  const [email, setEmail] = useState('')

  const handleEarlyAccess = (e) => {
    e.preventDefault()
    // TODO: Implement early access signup
    console.log('Early access signup:', email)
  }

  return (
    <div className="gradient-bg">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-5xl lg:text-7xl font-bold text-secondary-900 mb-6">
                Professional AI{' '}
                <span className="text-gradient">Headshots</span>{' '}
                in Minutes
              </h1>
              <p className="text-xl lg:text-2xl text-secondary-600 mb-8 max-w-3xl mx-auto">
                From LinkedIn to Dating Apps - Perfect Photos for Every Occasion. 
                No studio, no stylist, just one upload and your perfect headshot.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            >
              <Link
                to="/generate"
                className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-2"
              >
                <Camera className="w-5 h-5" />
                Generate Your Headshot
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                to="/gallery"
                className="btn-secondary text-lg px-8 py-4 inline-flex items-center gap-2"
              >
                <Sparkles className="w-5 h-5" />
                See Examples
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex items-center justify-center gap-6 text-sm text-secondary-500"
            >
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span>10,000+ professionals</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>4.9/5 rating</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Choose Your Perfect Style
            </h2>
            <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
              Whether you're a tech founder, creative professional, or looking for love - 
              we've got the right headshot style for you.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
            {[
              {
                icon: '💼',
                title: 'Professional',
                description: 'Perfect for LinkedIn, resumes, and corporate profiles',
                styles: ['Corporate', 'Executive', 'Modern Business', 'Outdoor Pro', 'Library']
              },
              {
                icon: '🌿',
                title: 'Casual & Lifestyle',
                description: 'Natural, approachable photos for personal branding',
                styles: ['Golden Hour', 'Coffee Shop', 'Beach Vibes', 'Park Setting']
              },
              {
                icon: '💖',
                title: 'Dating & Social',
                description: 'Attractive photos for dating apps and social media',
                styles: ['Romantic', 'Fun & Playful', 'Elegant', 'Adventurous']
              },
              {
                icon: '🎨',
                title: 'Creative & Branding',
                description: 'Artistic shots for creatives and entrepreneurs',
                styles: ['Artistic', 'Urban Edge', 'Studio Pro', 'Vintage', 'Minimalist']
              },
              {
                icon: '🎭',
                title: 'Actor & Model',
                description: 'Professional headshots for casting and portfolios',
                styles: ['Commercial', 'Dramatic', 'Fashion', 'Editorial']
              }
            ].map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card text-center hover:shadow-lg transition-shadow duration-300"
              >
                <div className="text-4xl mb-4">{category.icon}</div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                  {category.title}
                </h3>
                <p className="text-secondary-600 mb-4">{category.description}</p>
                <div className="space-y-1">
                  {category.styles.map((style) => (
                    <div key={style} className="text-sm text-secondary-500">
                      • {style}
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 gradient-bg">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-secondary-600">
              Pay once, use forever. No subscriptions, no hidden fees.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                name: 'Free Tier',
                price: '$0',
                period: 'forever',
                description: '5 headshots every 15 days',
                features: [
                  '5 headshots per 15 days',
                  'Watermarked previews',
                  '24-hour file retention',
                  'Professional styles only',
                  'Ad-supported'
                ],
                cta: 'Start Free',
                popular: false
              },
              {
                name: 'Starter Pack',
                price: '$19',
                period: 'one-time',
                description: '40 professional headshots',
                features: [
                  '40 headshots (all styles)',
                  'All style categories',
                  'No watermarks',
                  '30-day retention',
                  'Priority processing',
                  'Email support'
                ],
                cta: 'Get Started',
                popular: true
              },
              {
                name: 'Pro Pack',
                price: '$35',
                period: 'one-time',
                description: '100 headshots with premium features',
                features: [
                  '100 headshots (all styles)',
                  'Advanced customization',
                  'Bulk download (ZIP)',
                  '90-day retention',
                  'API access (future)',
                  'Priority support (24h)'
                ],
                cta: 'Go Pro',
                popular: false
              }
            ].map((plan) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className={`card relative ${plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    {plan.name}
                  </h3>
                  <div className="mb-2">
                    <span className="text-4xl font-bold text-secondary-900">{plan.price}</span>
                    <span className="text-secondary-500 ml-1">/{plan.period}</span>
                  </div>
                  <p className="text-secondary-600">{plan.description}</p>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-secondary-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  to={plan.price === '$0' ? '/generate' : '/auth'}
                  className={`w-full text-center py-3 px-6 rounded-lg font-medium transition-colors duration-200 ${
                    plan.popular
                      ? 'bg-primary-600 hover:bg-primary-700 text-white'
                      : 'bg-secondary-100 hover:bg-secondary-200 text-secondary-900'
                  }`}
                >
                  {plan.cta}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-secondary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">
            Ready to Transform Your Professional Image?
          </h2>
          <p className="text-xl text-secondary-300 mb-8 max-w-2xl mx-auto">
            Join thousands of professionals who've upgraded their headshots with HeadGenius Pro.
          </p>
          <Link
            to="/generate"
            className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-4 px-8 rounded-lg text-lg inline-flex items-center gap-2 transition-colors duration-200"
          >
            <Camera className="w-5 h-5" />
            Generate Your First Headshot
            <ArrowRight className="w-5 h-5" />
          </Link>
        </div>
      </section>
    </div>
  )
}

export default LandingPage
