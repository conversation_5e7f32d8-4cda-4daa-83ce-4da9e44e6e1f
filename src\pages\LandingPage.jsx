import { useState, useEffect, useRef } from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Camera, Sparkles, Users, Star, ArrowRight, Check, Zap, Heart } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const LandingPage = () => {
  const [email, setEmail] = useState('')
  const heroRef = useRef(null)
  const featuresRef = useRef(null)
  const statsRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero floating elements
      gsap.to('.floating-element', {
        y: -30,
        x: 20,
        rotation: 360,
        duration: 6,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 1
      })

      // Magical sparkles
      gsap.to('.magic-sparkle', {
        scale: [1, 1.5, 1],
        opacity: [0.3, 1, 0.3],
        rotation: 360,
        duration: 3,
        ease: 'power2.inOut',
        repeat: -1,
        stagger: 0.5
      })

      // Features cards animation
      gsap.fromTo('.feature-card',
        { y: 100, opacity: 0, scale: 0.8 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 1,
          stagger: 0.2,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: featuresRef.current,
            start: 'top 80%'
          }
        }
      )

      // Stats counter animation
      gsap.fromTo('.stat-number',
        { textContent: 0 },
        {
          textContent: (i, target) => target.getAttribute('data-value'),
          duration: 2,
          ease: 'power2.out',
          snap: { textContent: 1 },
          scrollTrigger: {
            trigger: statsRef.current,
            start: 'top 80%'
          }
        }
      )

      // Pulse effect for CTA buttons
      gsap.to('.pulse-button', {
        boxShadow: '0 0 0 0 rgba(59, 130, 246, 0.7)',
        duration: 0.6,
        ease: 'power2.out',
        repeat: -1,
        yoyo: true
      })
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const handleEarlyAccess = (e) => {
    e.preventDefault()
    // TODO: Implement early access signup
    console.log('Early access signup:', email)
  }

  return (
    <div className="gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 lg:py-32">
        {/* Floating background elements */}
        <div className="floating-element absolute top-20 left-10 text-primary-400 opacity-20">
          <Camera className="w-16 h-16" />
        </div>
        <div className="floating-element absolute top-40 right-20 text-accent-400 opacity-15">
          <Sparkles className="w-20 h-20" />
        </div>
        <div className="floating-element absolute bottom-40 left-20 text-primary-500 opacity-25">
          <Users className="w-12 h-12" />
        </div>
        <div className="floating-element absolute bottom-20 right-10 text-accent-500 opacity-20">
          <Star className="w-14 h-14" />
        </div>

        {/* Magic sparkles */}
        <div className="magic-sparkle absolute top-32 left-1/4 w-3 h-3 bg-yellow-400 rounded-full"></div>
        <div className="magic-sparkle absolute top-60 right-1/3 w-2 h-2 bg-pink-400 rounded-full"></div>
        <div className="magic-sparkle absolute bottom-60 left-1/3 w-4 h-4 bg-blue-400 rounded-full"></div>
        <div className="magic-sparkle absolute bottom-32 right-1/4 w-2 h-2 bg-green-400 rounded-full"></div>

        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-5xl lg:text-7xl font-bold text-secondary-900 mb-6">
                Professional AI{' '}
                <span className="text-gradient">Headshots</span>{' '}
                in Minutes
              </h1>
              <p className="text-xl lg:text-2xl text-secondary-600 mb-8 max-w-3xl mx-auto">
                From LinkedIn to Dating Apps - Perfect Photos for Every Occasion. 
                No studio, no stylist, just one upload and your perfect headshot.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            >
              <Link
                to="/generate"
                className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-2"
              >
                <Camera className="w-5 h-5" />
                Generate Your Headshot
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                to="/gallery"
                className="btn-secondary text-lg px-8 py-4 inline-flex items-center gap-2"
              >
                <Sparkles className="w-5 h-5" />
                See Examples
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex items-center justify-center gap-6 text-sm text-secondary-500"
            >
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span>10,000+ professionals</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span>4.9/5 rating</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white" ref={featuresRef}>
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Choose Your Perfect Style
            </h2>
            <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
              Whether you're a tech founder, creative professional, or looking for love -
              we've got the right headshot style for you.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
            {[
              {
                icon: '💼',
                title: 'Professional',
                description: 'Perfect for LinkedIn, resumes, and corporate profiles',
                styles: ['Corporate', 'Executive', 'Modern Business', 'Outdoor Pro', 'Library']
              },
              {
                icon: '🌿',
                title: 'Casual & Lifestyle',
                description: 'Natural, approachable photos for personal branding',
                styles: ['Golden Hour', 'Coffee Shop', 'Beach Vibes', 'Park Setting']
              },
              {
                icon: '💖',
                title: 'Dating & Social',
                description: 'Attractive photos for dating apps and social media',
                styles: ['Romantic', 'Fun & Playful', 'Elegant', 'Adventurous']
              },
              {
                icon: '🎨',
                title: 'Creative & Branding',
                description: 'Artistic shots for creatives and entrepreneurs',
                styles: ['Artistic', 'Urban Edge', 'Studio Pro', 'Vintage', 'Minimalist']
              },
              {
                icon: '🎭',
                title: 'Actor & Model',
                description: 'Professional headshots for casting and portfolios',
                styles: ['Commercial', 'Dramatic', 'Fashion', 'Editorial']
              }
            ].map((category, index) => (
              <motion.div
                key={category.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="feature-card card text-center hover:shadow-xl transition-all duration-300 group"
                whileHover={{ y: -10, scale: 1.02 }}
              >
                <motion.div
                  className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300"
                  whileHover={{ rotate: [0, -10, 10, 0] }}
                  transition={{ duration: 0.5 }}
                >
                  {category.icon}
                </motion.div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                  {category.title}
                </h3>
                <p className="text-secondary-600 mb-4">{category.description}</p>
                <div className="space-y-1">
                  {category.styles.map((style) => (
                    <div key={style} className="text-sm text-secondary-500">
                      • {style}
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 gradient-bg">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-secondary-600">
              Pay once, use forever. No subscriptions, no hidden fees.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                name: 'Free Trial',
                price: '$0',
                period: 'lifetime',
                description: '5 headshots to try HeadGenius',
                features: [
                  '5 headshots total (lifetime)',
                  'Watermarked previews',
                  '24-hour file retention',
                  'Professional styles only',
                  'Then upgrade to continue'
                ],
                cta: 'Start Free',
                popular: false
              },
              {
                name: 'Starter Pack',
                price: '$19',
                period: 'one-time',
                description: '40 professional headshots',
                features: [
                  '40 headshots (all styles)',
                  'All style categories',
                  'No watermarks',
                  '30-day retention',
                  'Priority processing',
                  'Email support'
                ],
                cta: 'Get Started',
                popular: true
              },
              {
                name: 'Pro Pack',
                price: '$35',
                period: 'one-time',
                description: '100 headshots with premium features',
                features: [
                  '100 headshots (all styles)',
                  'Advanced customization',
                  'Bulk download (ZIP)',
                  '90-day retention',
                  'API access (future)',
                  'Priority support (24h)'
                ],
                cta: 'Go Pro',
                popular: false
              }
            ].map((plan) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className={`card relative ${plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    {plan.name}
                  </h3>
                  <div className="mb-2">
                    <span className="text-4xl font-bold text-secondary-900">{plan.price}</span>
                    <span className="text-secondary-500 ml-1">/{plan.period}</span>
                  </div>
                  <p className="text-secondary-600">{plan.description}</p>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-3">
                      <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-secondary-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  to={plan.price === '$0' ? '/generate' : '/auth'}
                  className={`w-full text-center py-3 px-6 rounded-lg font-medium transition-colors duration-200 ${
                    plan.popular
                      ? 'bg-primary-600 hover:bg-primary-700 text-white'
                      : 'bg-secondary-100 hover:bg-secondary-200 text-secondary-900'
                  }`}
                >
                  {plan.cta}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm" ref={statsRef}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            {[
              { number: 50000, label: 'Headshots Generated', icon: Camera, suffix: '+' },
              { number: 15000, label: 'Happy Customers', icon: Users, suffix: '+' },
              { number: 98, label: 'Satisfaction Rate', icon: Heart, suffix: '%' },
              { number: 24, label: 'Countries Served', icon: Zap, suffix: '+' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-bold text-secondary-900 mb-2">
                  <span className="stat-number" data-value={stat.number}>0</span>
                  <span>{stat.suffix}</span>
                </div>
                <p className="text-secondary-600 font-medium">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-secondary-900 text-white relative overflow-hidden">
        {/* Animated background elements */}
        <div className="floating-element absolute top-10 left-10 text-primary-400 opacity-10">
          <Sparkles className="w-20 h-20" />
        </div>
        <div className="floating-element absolute bottom-10 right-10 text-accent-400 opacity-10">
          <Camera className="w-16 h-16" />
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold mb-4">
              Ready to Transform Your Professional Image?
            </h2>
            <p className="text-xl text-secondary-300 mb-8 max-w-2xl mx-auto">
              Join thousands of professionals who've upgraded their headshots with HeadGenius Pro.
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/generate"
                className="pulse-button bg-primary-600 hover:bg-primary-700 text-white font-medium py-4 px-8 rounded-lg text-lg inline-flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <Camera className="w-5 h-5" />
                Generate Your First Headshot
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default LandingPage
