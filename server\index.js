import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import multer from 'multer'
import { generateHeadshot, getGenerationStatus, handleWebhook } from '../src/api/generate.js'
import { createCheckoutSession, verifyPaymentSession, getPaymentHistory, handleStripeWebhook } from '../src/api/payments.js'
import { cleanupService } from '../src/services/cleanupService.js'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true)
    } else {
      cb(new Error('Only image files are allowed'), false)
    }
  }
})

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'HeadGenius Pro API'
  })
})

// Image upload endpoint
app.post('/api/upload', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' })
    }

    const { userId } = req.body
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' })
    }

    // Import storage helper
    const { storage } = await import('../src/lib/supabase.js')

    // Check storage quota
    const { hasQuota, error: quotaError } = await storage.checkStorageQuota(userId)
    if (quotaError || !hasQuota) {
      return res.status(413).json({
        error: 'Storage quota exceeded. Please upgrade your plan or delete old files.'
      })
    }

    // Upload to Supabase Storage
    const { data, error } = await storage.uploadInputImage(req.file, userId)

    if (error) {
      console.error('Storage upload error:', error)
      return res.status(500).json({
        error: 'Failed to upload image to storage',
        details: error.message
      })
    }

    res.json({
      success: true,
      imageUrl: data.publicUrl,
      filename: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype,
      storagePath: data.path
    })

  } catch (error) {
    console.error('Upload error:', error)
    res.status(500).json({
      error: 'Failed to upload image',
      details: error.message
    })
  }
})

// Generation endpoints
app.post('/api/generate', generateHeadshot)
app.get('/api/generate/:predictionId/status', getGenerationStatus)
app.post('/api/webhooks/replicate', handleWebhook)

// Payment endpoints
app.post('/api/payments/create-checkout-session', createCheckoutSession)
app.get('/api/payments/verify-session/:sessionId', verifyPaymentSession)
app.get('/api/payments/history/:userId', getPaymentHistory)

// Cleanup endpoints
app.post('/api/cleanup/run', async (req, res) => {
  try {
    await cleanupService.runCleanup()
    res.json({ success: true, message: 'Cleanup completed' })
  } catch (error) {
    res.status(500).json({ error: 'Cleanup failed', details: error.message })
  }
})

app.get('/api/cleanup/stats', async (req, res) => {
  try {
    const stats = await cleanupService.getStorageStats()
    res.json({ success: true, stats })
  } catch (error) {
    res.status(500).json({ error: 'Failed to get stats', details: error.message })
  }
})

app.delete('/api/cleanup/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const deletedCount = await cleanupService.cleanupUserFiles(userId)
    res.json({ success: true, deletedCount })
  } catch (error) {
    res.status(500).json({ error: 'User cleanup failed', details: error.message })
  }
})

// User credits endpoint (placeholder)
app.get('/api/user/:userId/credits', async (req, res) => {
  try {
    const { userId } = req.params
    
    // TODO: Get from Supabase
    const mockCredits = {
      total: 40,
      used: 5,
      remaining: 35,
      freeUsed: 2,
      lastFreeReset: new Date().toISOString(),
      tier: 'starter'
    }

    res.json({
      success: true,
      credits: mockCredits
    })

  } catch (error) {
    console.error('Credits error:', error)
    res.status(500).json({ 
      error: 'Failed to get user credits',
      details: error.message 
    })
  }
})

// Stripe webhook endpoint
app.post('/api/webhooks/stripe', express.raw({ type: 'application/json' }), handleStripeWebhook)

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error)
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' })
    }
  }
  
  res.status(500).json({ 
    error: 'Internal server error',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 HeadGenius Pro API server running on port ${PORT}`)
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`)
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`)

  // Start cleanup service in production
  if (process.env.NODE_ENV === 'production') {
    cleanupService.start()
    console.log(`🧹 Cleanup service started`)
  }
})
