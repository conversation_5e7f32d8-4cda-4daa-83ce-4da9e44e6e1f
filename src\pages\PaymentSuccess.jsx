import { useEffect, useState } from 'react'
import { useSearchParams, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { CheckCircle, CreditCard, ArrowRight, Sparkles } from 'lucide-react'
import { verifyPaymentSession } from '../lib/stripe'
import { useAuthStore } from '../store/authStore'

const PaymentSuccess = () => {
  const [searchParams] = useSearchParams()
  const [paymentData, setPaymentData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const { loadUserCredits } = useAuthStore()

  useEffect(() => {
    const sessionId = searchParams.get('session_id')
    
    if (sessionId) {
      verifyPayment(sessionId)
    } else {
      setError('No payment session found')
      setLoading(false)
    }
  }, [searchParams])

  const verifyPayment = async (sessionId) => {
    try {
      const result = await verifyPaymentSession(sessionId)
      setPaymentData(result.session)
      
      // Reload user credits to reflect the purchase
      await loadUserCredits()
      
    } catch (error) {
      console.error('Payment verification error:', error)
      setError('Failed to verify payment')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-secondary-600">Verifying your payment...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="card max-w-md text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">❌</span>
          </div>
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">
            Payment Verification Failed
          </h1>
          <p className="text-secondary-600 mb-6">{error}</p>
          <Link to="/pricing" className="btn-primary">
            Back to Pricing
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen gradient-bg py-12">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-2xl mx-auto"
        >
          {/* Success Header */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <CheckCircle className="w-10 h-10 text-green-600" />
            </motion.div>
            
            <h1 className="text-4xl font-bold text-secondary-900 mb-4">
              Payment Successful! 🎉
            </h1>
            <p className="text-xl text-secondary-600">
              Your credits have been added to your account
            </p>
          </div>

          {/* Payment Details */}
          <div className="card mb-8">
            <h2 className="text-xl font-semibold text-secondary-900 mb-6 flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Payment Details
            </h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-secondary-200">
                <span className="text-secondary-600">Amount Paid</span>
                <span className="font-semibold text-secondary-900">
                  ${(paymentData?.amount / 100).toFixed(2)} {paymentData?.currency?.toUpperCase()}
                </span>
              </div>
              
              <div className="flex justify-between items-center py-2 border-b border-secondary-200">
                <span className="text-secondary-600">Credits Added</span>
                <span className="font-semibold text-green-600">
                  +{paymentData?.creditsAdded} credits
                </span>
              </div>
              
              <div className="flex justify-between items-center py-2 border-b border-secondary-200">
                <span className="text-secondary-600">Payment ID</span>
                <span className="font-mono text-sm text-secondary-700">
                  {paymentData?.id}
                </span>
              </div>
              
              <div className="flex justify-between items-center py-2">
                <span className="text-secondary-600">Status</span>
                <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  <CheckCircle className="w-3 h-3" />
                  Paid
                </span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="card mb-8">
            <h2 className="text-xl font-semibold text-secondary-900 mb-6">
              What's Next?
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-start gap-4 p-4 bg-primary-50 rounded-lg">
                <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  1
                </div>
                <div>
                  <h3 className="font-medium text-secondary-900 mb-1">
                    Start Generating Headshots
                  </h3>
                  <p className="text-secondary-600 text-sm">
                    Your credits are ready to use. Create amazing headshots with our AI.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4 p-4 bg-accent-50 rounded-lg">
                <div className="w-8 h-8 bg-accent-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  2
                </div>
                <div>
                  <h3 className="font-medium text-secondary-900 mb-1">
                    Explore All Styles
                  </h3>
                  <p className="text-secondary-600 text-sm">
                    Try different styles: Professional, Casual, Dating, Creative, and Actor/Model.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-4 p-4 bg-secondary-50 rounded-lg">
                <div className="w-8 h-8 bg-secondary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  3
                </div>
                <div>
                  <h3 className="font-medium text-secondary-900 mb-1">
                    Download & Share
                  </h3>
                  <p className="text-secondary-600 text-sm">
                    Get high-resolution downloads without watermarks and share your results.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/generate"
              className="btn-primary text-lg px-8 py-4 inline-flex items-center justify-center gap-2"
            >
              <Sparkles className="w-5 h-5" />
              Start Generating
              <ArrowRight className="w-5 h-5" />
            </Link>
            
            <Link
              to="/dashboard"
              className="btn-secondary text-lg px-8 py-4 inline-flex items-center justify-center gap-2"
            >
              View Dashboard
            </Link>
          </div>

          {/* Support Note */}
          <div className="text-center mt-8">
            <p className="text-secondary-600 text-sm">
              Need help? Contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
                <EMAIL>
              </a>
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default PaymentSuccess
