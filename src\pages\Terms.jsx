import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { FileText, Scale, Shield, AlertTriangle, CheckCircle, XCircle, Users, Gavel } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const Terms = () => {
  const heroRef = useRef(null)
  const contentRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.terms-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.terms-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating legal icons
      gsap.to('.floating-legal', {
        y: -15,
        rotation: 3,
        duration: 2.5,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.4
      })

      // Content sections animation
      gsap.fromTo('.terms-section', 
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: contentRef.current,
            start: 'top 80%'
          }
        }
      )

      // Quick summary animation
      gsap.fromTo('.summary-item', 
        { scale: 0.9, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: '.summary-container',
            start: 'top 80%'
          }
        }
      )
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const quickSummary = [
    {
      icon: CheckCircle,
      title: 'What You Can Do',
      items: [
        'Generate professional headshots for personal and commercial use',
        'Download and use your generated images',
        'Share your headshots on social media and professional platforms',
        'Use images for business purposes, marketing, and branding'
      ]
    },
    {
      icon: XCircle,
      title: 'What You Cannot Do',
      items: [
        'Upload photos of other people without consent',
        'Generate inappropriate or harmful content',
        'Resell or redistribute our AI technology',
        'Use the service for illegal activities'
      ]
    }
  ]

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="floating-legal absolute top-10 left-10 text-primary-400 opacity-20">
            <Scale className="w-16 h-16" />
          </div>
          <div className="floating-legal absolute top-20 right-20 text-accent-400 opacity-20">
            <Gavel className="w-12 h-12" />
          </div>
          <div className="floating-legal absolute bottom-20 left-20 text-primary-400 opacity-20">
            <FileText className="w-14 h-14" />
          </div>
          
          <h1 className="terms-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            Terms of <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Service</span>
          </h1>
          <p className="terms-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            The legal agreement between you and HeadGenius Pro for using our AI headshot generation service.
          </p>
          <div className="mt-8 text-sm text-secondary-500">
            Last updated: January 15, 2025
          </div>
        </div>
      </section>

      {/* Quick Summary */}
      <section className="py-16 summary-container">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-secondary-900 mb-4">Quick Summary</h2>
            <p className="text-secondary-600">Key points about using HeadGenius Pro</p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {quickSummary.map((section, index) => (
              <motion.div
                key={index}
                className="summary-item card"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    section.icon === CheckCircle 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-red-100 text-red-600'
                  }`}>
                    <section.icon className="w-5 h-5" />
                  </div>
                  <h3 className="text-xl font-bold text-secondary-900">{section.title}</h3>
                </div>
                <ul className="space-y-3">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-3">
                      <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                        section.icon === CheckCircle ? 'bg-green-500' : 'bg-red-500'
                      }`}></div>
                      <span className="text-secondary-700">{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Terms */}
      <section className="py-16" ref={contentRef}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto space-y-12">
            
            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Users className="w-6 h-6 text-primary-600" />
                Acceptance of Terms
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  By accessing and using HeadGenius Pro ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. 
                  If you do not agree to abide by the above, please do not use this service.
                </p>
                <p>
                  These Terms of Service ("Terms") govern your use of our website and services operated by HeadGenius Pro ("us", "we", or "our").
                </p>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <FileText className="w-6 h-6 text-primary-600" />
                Description of Service
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  HeadGenius Pro is an AI-powered platform that generates professional headshots from user-uploaded photos. Our service includes:
                </p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>AI-powered headshot generation from uploaded photos</li>
                  <li>Multiple professional styles and backgrounds</li>
                  <li>High-resolution image downloads</li>
                  <li>Cloud storage for generated images (time-limited based on subscription)</li>
                  <li>Customer support and technical assistance</li>
                </ul>
                <p>
                  We reserve the right to modify, suspend, or discontinue any part of the service at any time with reasonable notice.
                </p>
              </div>
            </div>

            <div className="terms-section card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Shield className="w-6 h-6 text-primary-600" />
                User Responsibilities
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">When using our service, you agree to:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Only upload photos of yourself or photos for which you have explicit consent</li>
                  <li>Provide accurate and complete information when creating your account</li>
                  <li>Maintain the security of your account credentials</li>
                  <li>Use the service in compliance with all applicable laws and regulations</li>
                  <li>Not attempt to reverse engineer, hack, or exploit our AI technology</li>
                  <li>Not upload inappropriate, offensive, or illegal content</li>
                </ul>
                <p><strong>Important:</strong> You are solely responsible for ensuring you have the right to upload and process any photos you submit to our service.</p>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Intellectual Property Rights</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4"><strong>Your Content:</strong></p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>You retain ownership of photos you upload to our service</li>
                  <li>You own the generated headshots created from your photos</li>
                  <li>You grant us a limited license to process your photos for headshot generation</li>
                  <li>You can use generated headshots for any lawful purpose, including commercial use</li>
                </ul>
                <p className="mb-4"><strong>Our Technology:</strong></p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>HeadGenius Pro owns all rights to our AI technology, algorithms, and platform</li>
                  <li>Our service, website, and software are protected by copyright and other intellectual property laws</li>
                  <li>You may not copy, modify, or distribute our technology without written permission</li>
                </ul>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Subscription and Billing</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">Our service offers multiple subscription tiers:</p>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="bg-secondary-50 p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Free Tier</h4>
                    <p className="text-sm">3 generations per week, 24-hour retention</p>
                  </div>
                  <div className="bg-secondary-50 p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Starter Pack - $19</h4>
                    <p className="text-sm">Unlimited generations, 30-day retention</p>
                  </div>
                  <div className="bg-secondary-50 p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Pro Pack - $35</h4>
                    <p className="text-sm">Premium features, 90-day retention, priority support</p>
                  </div>
                </div>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Subscriptions are billed monthly and automatically renew</li>
                  <li>You can cancel your subscription at any time from your account settings</li>
                  <li>Cancellation takes effect at the end of your current billing period</li>
                  <li>No refunds for partial months, except as outlined in our Refund Policy</li>
                  <li>Prices may change with 30 days notice to existing subscribers</li>
                </ul>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <AlertTriangle className="w-6 h-6 text-amber-600" />
                Prohibited Uses
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">You may not use our service for:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Creating deepfakes or misleading content</li>
                  <li>Generating images of minors without parental consent</li>
                  <li>Creating content that violates others' privacy or rights</li>
                  <li>Harassment, bullying, or impersonation</li>
                  <li>Any illegal activities or content</li>
                  <li>Circumventing usage limits or security measures</li>
                  <li>Automated or bulk processing without authorization</li>
                </ul>
                <p>Violation of these terms may result in immediate account suspension or termination.</p>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Disclaimers and Limitations</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  <strong>Service Availability:</strong> We strive for 99.9% uptime but cannot guarantee uninterrupted service. 
                  Maintenance, updates, or technical issues may temporarily affect availability.
                </p>
                <p className="mb-4">
                  <strong>AI Accuracy:</strong> While our AI technology is advanced, generated headshots may not always perfectly 
                  represent the uploaded photo. Results can vary based on photo quality and other factors.
                </p>
                <p className="mb-4">
                  <strong>Limitation of Liability:</strong> To the maximum extent permitted by law, HeadGenius Pro shall not be 
                  liable for any indirect, incidental, special, or consequential damages arising from your use of the service.
                </p>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Termination</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  Either party may terminate this agreement at any time. You can delete your account from your account settings. 
                  We may terminate or suspend your account for violations of these terms.
                </p>
                <p className="mb-4">Upon termination:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Your access to the service will be immediately revoked</li>
                  <li>Your data will be deleted according to our retention policy</li>
                  <li>You remain responsible for any outstanding charges</li>
                  <li>Provisions regarding intellectual property and limitations of liability survive termination</li>
                </ul>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Governing Law</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  These Terms shall be governed by and construed in accordance with the laws of the State of California, 
                  United States, without regard to its conflict of law provisions.
                </p>
                <p>
                  Any disputes arising from these Terms or your use of the service shall be resolved through binding arbitration 
                  in San Francisco, California, except for claims that may be brought in small claims court.
                </p>
              </div>
            </div>

            <div className="terms-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Changes to Terms</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  We reserve the right to modify these Terms at any time. We will notify users of significant changes via email 
                  or through our service. Your continued use of the service after changes constitutes acceptance of the new Terms.
                </p>
                <p>
                  We encourage you to review these Terms periodically for any updates.
                </p>
              </div>
            </div>

            <div className="terms-section card bg-secondary-900 text-white">
              <h2 className="text-2xl font-bold mb-4">Contact Information</h2>
              <div className="prose prose-lg text-secondary-300">
                <p className="mb-4">
                  If you have any questions about these Terms of Service, please contact us:
                </p>
                <ul className="list-none space-y-2">
                  <li><strong>Email:</strong> <EMAIL></li>
                  <li><strong>Support:</strong> <EMAIL></li>
                  <li><strong>Address:</strong> San Francisco, CA, United States</li>
                </ul>
              </div>
            </div>

          </div>
        </div>
      </section>
    </div>
  )
}

export default Terms
