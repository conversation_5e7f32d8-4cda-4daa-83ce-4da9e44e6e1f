import { useEffect, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { 
  Search, 
  ChevronDown, 
  ChevronRight, 
  Book, 
  Video, 
  MessageCircle, 
  Camera, 
  CreditCard, 
  Settings, 
  Shield,
  HelpCircle,
  Lightbulb,
  Zap
} from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const HelpCenter = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [expandedFAQ, setExpandedFAQ] = useState(null)
  
  const heroRef = useRef(null)
  const categoriesRef = useRef(null)
  const faqRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.help-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.help-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Search bar animation
      gsap.fromTo('.search-container', 
        { scale: 0.8, opacity: 0 },
        { scale: 1, opacity: 1, duration: 0.8, delay: 0.5, ease: 'back.out(1.4)' }
      )

      // Categories animation
      gsap.fromTo('.category-card', 
        { y: 80, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: categoriesRef.current,
            start: 'top 80%'
          }
        }
      )

      // FAQ animation
      gsap.fromTo('.faq-item', 
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: faqRef.current,
            start: 'top 80%'
          }
        }
      )

      // Floating help icons
      gsap.to('.floating-help-icon', {
        y: -15,
        rotation: 5,
        duration: 2.5,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.3
      })
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const categories = [
    {
      id: 'getting-started',
      name: 'Getting Started',
      icon: Lightbulb,
      description: 'Learn the basics of HeadGenius Pro',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'photo-upload',
      name: 'Photo Upload',
      icon: Camera,
      description: 'Tips for uploading the perfect photo',
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'billing',
      name: 'Billing & Plans',
      icon: CreditCard,
      description: 'Subscription and payment questions',
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'account',
      name: 'Account Settings',
      icon: Settings,
      description: 'Manage your account preferences',
      color: 'from-orange-500 to-orange-600'
    },
    {
      id: 'privacy',
      name: 'Privacy & Security',
      icon: Shield,
      description: 'Data protection and security info',
      color: 'from-red-500 to-red-600'
    },
    {
      id: 'troubleshooting',
      name: 'Troubleshooting',
      icon: Zap,
      description: 'Fix common issues quickly',
      color: 'from-yellow-500 to-yellow-600'
    }
  ]

  const faqs = [
    {
      category: 'getting-started',
      question: 'How do I create my first headshot?',
      answer: 'Simply upload a clear photo of yourself, choose your preferred style, and click generate. Our AI will create professional headshots in minutes.'
    },
    {
      category: 'getting-started',
      question: 'What makes a good source photo?',
      answer: 'Use a high-resolution photo with good lighting, clear facial features, and minimal background distractions. Avoid sunglasses, hats, or heavy shadows.'
    },
    {
      category: 'photo-upload',
      question: 'What file formats are supported?',
      answer: 'We support JPG, PNG, and WEBP formats. Files should be under 10MB and at least 512x512 pixels for best results.'
    },
    {
      category: 'photo-upload',
      question: 'Can I upload multiple photos at once?',
      answer: 'Pro subscribers can upload up to 5 photos per generation for better variety. Free users can upload 1 photo at a time.'
    },
    {
      category: 'billing',
      question: 'What\'s included in the free tier?',
      answer: 'Free users get 3 headshot generations per week with 24-hour image retention and access to basic styles.'
    },
    {
      category: 'billing',
      question: 'How do I upgrade to Pro?',
      answer: 'Click the "Upgrade" button in your dashboard, choose your plan ($19 Starter or $35 Pro), and complete the secure checkout process.'
    },
    {
      category: 'billing',
      question: 'Can I cancel my subscription anytime?',
      answer: 'Yes, you can cancel anytime from your account settings. You\'ll retain Pro features until the end of your billing period.'
    },
    {
      category: 'account',
      question: 'How do I change my email address?',
      answer: 'Go to Account Settings > Profile Information and update your email. You\'ll need to verify the new email address.'
    },
    {
      category: 'privacy',
      question: 'How long do you keep my photos?',
      answer: 'Free tier: 24 hours, Starter: 30 days, Pro: 90 days. All images are automatically deleted after the retention period.'
    },
    {
      category: 'privacy',
      question: 'Is my data secure?',
      answer: 'Yes, we use enterprise-grade encryption and never share your photos with third parties. All processing happens on secure servers.'
    },
    {
      category: 'troubleshooting',
      question: 'Why is my generation taking so long?',
      answer: 'Generations typically take 2-5 minutes. During peak times, it may take longer. Pro users get priority processing.'
    },
    {
      category: 'troubleshooting',
      question: 'The generated headshot doesn\'t look like me',
      answer: 'Try uploading a clearer source photo with better lighting. Make sure your face is clearly visible and well-lit.'
    }
  ]

  const filteredFAQs = selectedCategory === 'all' 
    ? faqs 
    : faqs.filter(faq => faq.category === selectedCategory)

  const searchFilteredFAQs = filteredFAQs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          {/* Floating help icons */}
          <div className="floating-help-icon absolute top-10 left-10 text-primary-400 opacity-20">
            <HelpCircle className="w-16 h-16" />
          </div>
          <div className="floating-help-icon absolute top-20 right-20 text-accent-400 opacity-20">
            <Book className="w-12 h-12" />
          </div>
          <div className="floating-help-icon absolute bottom-20 left-20 text-primary-400 opacity-20">
            <Video className="w-14 h-14" />
          </div>
          
          <h1 className="help-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            Help <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Center</span>
          </h1>
          <p className="help-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed mb-12">
            Find answers, tutorials, and support to get the most out of HeadGenius Pro
          </p>

          {/* Search Bar */}
          <div className="search-container max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-secondary-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search for help articles, FAQs, and guides..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-lg border border-secondary-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16" ref={categoriesRef}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-secondary-900 mb-4">Browse by Category</h2>
            <p className="text-secondary-600">Find help organized by topic</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                className="category-card card group hover:shadow-xl transition-all duration-300 cursor-pointer"
                whileHover={{ y: -5 }}
                onClick={() => setSelectedCategory(category.id)}
              >
                <div className={`w-12 h-12 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                  {category.name}
                </h3>
                <p className="text-secondary-600">{category.description}</p>
                <div className="mt-4 flex items-center text-primary-600 font-medium">
                  <span>Explore</span>
                  <ChevronRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200" />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm" ref={faqRef}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-secondary-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-secondary-600">Quick answers to common questions</p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                selectedCategory === 'all'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white border border-secondary-200 text-secondary-700 hover:border-primary-300'
              }`}
            >
              All
            </button>
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-white border border-secondary-200 text-secondary-700 hover:border-primary-300'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* FAQ Items */}
          <div className="max-w-4xl mx-auto space-y-4">
            {searchFilteredFAQs.map((faq, index) => (
              <motion.div
                key={index}
                className="faq-item card cursor-pointer"
                onClick={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-secondary-900 pr-4">
                    {faq.question}
                  </h3>
                  <ChevronDown 
                    className={`w-5 h-5 text-secondary-500 transition-transform duration-200 ${
                      expandedFAQ === index ? 'rotate-180' : ''
                    }`}
                  />
                </div>
                
                <AnimatePresence>
                  {expandedFAQ === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="pt-4 border-t border-secondary-100 mt-4">
                        <p className="text-secondary-600 leading-relaxed">{faq.answer}</p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {searchFilteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <HelpCircle className="w-16 h-16 text-secondary-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">No results found</h3>
              <p className="text-secondary-600">Try adjusting your search or browse by category</p>
            </div>
          )}
        </div>
      </section>

      {/* Contact Support CTA */}
      <section className="py-20 bg-secondary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold mb-4">Still Need Help?</h2>
            <p className="text-xl text-secondary-300 mb-8 max-w-2xl mx-auto">
              Can't find what you're looking for? Our support team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-primary-600 hover:bg-primary-700 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 inline-flex items-center gap-2"
              >
                <MessageCircle className="w-5 h-5" />
                Contact Support
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white/10 hover:bg-white/20 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 inline-flex items-center gap-2"
              >
                <Video className="w-5 h-5" />
                Watch Tutorials
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default HelpCenter
