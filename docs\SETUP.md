# HeadGenius Pro Setup Guide

## 🚀 Quick Start

### 1. **Database Setup (Supabase)**

#### Step 1: Create Project
1. Go to [Supabase](https://supabase.com) and create a new project
2. Wait for the project to be fully initialized

#### Step 2: Setup Database Schema
1. Go to SQL Editor in your Supabase dashboard
2. Copy the entire content from `database/schema.sql`
3. Paste and run it - this creates all tables, policies, and functions

#### Step 3: Setup Storage Buckets
1. Go to Storage in your Supabase dashboard
2. Create two buckets:
   - **Bucket name**: `user-uploads` (Private)
   - **Bucket name**: `generated-headshots` (Private)
3. The storage policies are automatically created by the schema

#### Step 4: Enable Realtime (Optional)
1. Go to Database → Replication
2. Enable realtime for `generations` table if you want live updates

#### Step 5: Setup Automatic Cleanup (Production)
For production, you can setup automatic cleanup using pg_cron:
1. Go to SQL Editor
2. Run: `CREATE EXTENSION IF NOT EXISTS pg_cron;`
3. Schedule daily cleanup:
```sql
SELECT cron.schedule('cleanup-expired-generations', '0 2 * * *', 'SELECT cleanup_expired_generations();');
SELECT cron.schedule('cleanup-orphaned-files', '0 3 * * *', 'SELECT cleanup_orphaned_files();');
```

**File Retention Periods:**
- **Free Tier**: 24 hours
- **Starter Pack**: 30 days
- **Pro Pack**: 90 days

### 2. **Replicate Setup**

1. Go to [Replicate](https://replicate.com)
2. Sign up for a free account (you get $10 free credits)
3. Go to Account Settings → API Tokens
4. Copy your API token to `.env` as `REPLICATE_API_TOKEN`

**Note:** You can start testing immediately with the free tier!

### 3. **Stripe Setup (For Payments)**

#### Step 1: Create Stripe Account
1. Go to [Stripe](https://stripe.com) and create an account
2. Complete the account verification process

#### Step 2: Get API Keys
1. In Stripe Dashboard, go to Developers → API Keys
2. Copy the **Publishable key** and **Secret key** (use test keys for development)
3. Add them to your `.env` file:
   ```
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   ```

#### Step 3: Create Products and Prices
1. In Stripe Dashboard, go to Products
2. Create two products:

**Starter Pack:**
- Name: "HeadGenius Starter Pack"
- Price: $19.00 (one-time payment)
- Copy the Price ID (starts with `price_`) to your code

**Pro Pack:**
- Name: "HeadGenius Pro Pack"  
- Price: $35.00 (one-time payment)
- Copy the Price ID (starts with `price_`) to your code

#### Step 4: Update Price IDs
In `src/lib/stripe.js`, update the `priceId` fields:
```javascript
export const PRICING_PLANS = {
  starter: {
    // ... other fields
    priceId: 'price_1234567890', // Your actual Stripe Price ID
  },
  pro: {
    // ... other fields  
    priceId: 'price_0987654321', // Your actual Stripe Price ID
  }
}
```

#### Step 5: Setup Webhooks
1. In Stripe Dashboard, go to Developers → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
3. Select events: `checkout.session.completed`, `payment_intent.payment_failed`
4. Copy the webhook secret to `.env` as `STRIPE_WEBHOOK_SECRET`

### 4. **Google AdSense (Optional - For Free Tier)**

1. Apply for [Google AdSense](https://adsense.google.com)
2. Once approved, get your Publisher ID and Ad Unit IDs
3. Add them to `.env`:
   ```
   GOOGLE_ADSENSE_CLIENT_ID=ca-pub-...
   GOOGLE_ADSENSE_SLOT_ID=...
   ```

### 5. **Environment Variables**

Your `.env` file should look like this:
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Replicate AI Configuration  
REPLICATE_API_TOKEN=r8_your_token_here

# Stripe Payment Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Application Configuration
VITE_APP_URL=http://localhost:5173
NODE_ENV=development
PORT=5173

# Optional: Google AdSense
GOOGLE_ADSENSE_CLIENT_ID=ca-pub-your_id
GOOGLE_ADSENSE_SLOT_ID=your_slot_id
```

### 6. **Run the Application**

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# In another terminal, start the API server
npm run server
```

Visit `http://localhost:5173` to see your application!

## 🔧 Development Workflow

### Testing Payments
1. Use Stripe test cards: `4242 4242 4242 4242`
2. Any future expiry date and CVC
3. Test the complete payment flow

### Testing AI Generation
1. Upload a clear headshot photo
2. Select any style
3. The generation will work with your Replicate free credits

### Database Management
- Use Supabase dashboard to view/edit data
- All tables have Row Level Security enabled
- Users can only access their own data

## 🚀 Deployment

### Frontend (Vercel)
1. Connect your GitHub repo to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Backend API
- Can be deployed on Vercel as serverless functions
- Or use Railway, Render, or similar for persistent server

### Domain Setup
1. Point your domain to Vercel
2. Update `VITE_APP_URL` in production environment
3. Update Stripe webhook URL to production domain

## 📊 Monitoring

### Key Metrics to Track
- User signups and conversions
- Generation success rates
- Payment completion rates
- Popular styles and usage patterns

### Recommended Tools
- Supabase Analytics (built-in)
- Stripe Dashboard (payments)
- Vercel Analytics (performance)
- Google Analytics (user behavior)

## 🆘 Troubleshooting

### Common Issues

**Replicate API Errors:**
- Check your API token is correct
- Ensure you have credits remaining
- Verify the model names are correct

**Stripe Payment Issues:**
- Confirm webhook endpoint is accessible
- Check webhook secret matches
- Verify price IDs are correct

**Supabase Connection:**
- Ensure RLS policies are properly set
- Check API keys are correct
- Verify user authentication flow

### Getting Help
- Check the browser console for errors
- Review server logs for API issues
- Test each component individually
- Use Stripe/Replicate test modes for debugging

## 🎯 Next Steps

Once everything is working:
1. **Content Creation:** Add blog posts and help documentation
2. **SEO Optimization:** Improve meta tags and structured data  
3. **Analytics:** Setup comprehensive tracking
4. **Marketing:** Launch social media and content marketing
5. **Scaling:** Monitor usage and optimize costs

Your HeadGenius Pro application is now ready to generate amazing headshots and process payments! 🎉
