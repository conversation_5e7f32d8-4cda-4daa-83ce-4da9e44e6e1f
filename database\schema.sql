-- HeadGenius Pro Database Schema
-- Run this in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User Credits Table
CREATE TABLE user_credits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    total_credits INTEGER DEFAULT 0,
    used_credits INTEGER DEFAULT 0,
    free_credits_used INTEGER DEFAULT 0,
    last_free_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'starter', 'pro')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Generations Table
CREATE TABLE generations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    style_id TEXT NOT NULL,
    style_name TEXT,
    input_image_url TEXT NOT NULL,
    output_image_url TEXT,
    replicate_prediction_id TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    cost DECIMAL(10,4) DEFAULT 0,
    gender TEXT DEFAULT 'auto',
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Payments Table
CREATE TABLE payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_payment_intent_id TEXT UNIQUE,
    stripe_session_id TEXT,
    amount INTEGER NOT NULL, -- Amount in cents
    currency TEXT DEFAULT 'usd',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'succeeded', 'failed', 'canceled')),
    credits_purchased INTEGER DEFAULT 0,
    tier_purchased TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Profiles Table (extended user info)
CREATE TABLE user_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free',
    total_generations INTEGER DEFAULT 0,
    last_generation_at TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Style Analytics Table (track popular styles)
CREATE TABLE style_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    style_id TEXT NOT NULL,
    generation_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0,
    avg_rating DECIMAL(3,2) DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(style_id)
);

-- Create indexes for better performance
CREATE INDEX idx_generations_user_id ON generations(user_id);
CREATE INDEX idx_generations_created_at ON generations(created_at DESC);
CREATE INDEX idx_generations_status ON generations(status);
CREATE INDEX idx_user_credits_user_id ON user_credits(user_id);
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_stripe_payment_intent_id ON payments(stripe_payment_intent_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_user_credits_updated_at BEFORE UPDATE ON user_credits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_style_analytics_updated_at BEFORE UPDATE ON style_analytics FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;
ALTER TABLE generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- User Credits Policies
CREATE POLICY "Users can view own credits" ON user_credits FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own credits" ON user_credits FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own credits" ON user_credits FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Generations Policies
CREATE POLICY "Users can view own generations" ON generations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own generations" ON generations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own generations" ON generations FOR UPDATE USING (auth.uid() = user_id);

-- Payments Policies
CREATE POLICY "Users can view own payments" ON payments FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own payments" ON payments FOR INSERT WITH CHECK (auth.uid() = user_id);

-- User Profiles Policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Style Analytics is public read
CREATE POLICY "Anyone can view style analytics" ON style_analytics FOR SELECT USING (true);

-- Function to initialize user data on signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (user_id, full_name)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');
    
    INSERT INTO user_credits (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user data on signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update style analytics
CREATE OR REPLACE FUNCTION update_style_analytics(style_id_param TEXT)
RETURNS VOID AS $$
BEGIN
    INSERT INTO style_analytics (style_id, generation_count, last_used_at)
    VALUES (style_id_param, 1, NOW())
    ON CONFLICT (style_id)
    DO UPDATE SET
        generation_count = style_analytics.generation_count + 1,
        last_used_at = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- SUPABASE STORAGE CONFIGURATION
-- =============================================

-- Create storage buckets (run these in Supabase Dashboard → Storage)
-- Bucket: 'user-uploads' (for input images)
-- Bucket: 'generated-headshots' (for output images)

-- Storage policies for user-uploads bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('user-uploads', 'user-uploads', false)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('generated-headshots', 'generated-headshots', false)
ON CONFLICT (id) DO NOTHING;

-- User uploads policies
CREATE POLICY "Users can upload their own images" ON storage.objects
FOR INSERT WITH CHECK (
    bucket_id = 'user-uploads'
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their own uploads" ON storage.objects
FOR SELECT USING (
    bucket_id = 'user-uploads'
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own uploads" ON storage.objects
FOR DELETE USING (
    bucket_id = 'user-uploads'
    AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Generated headshots policies
CREATE POLICY "Users can view their own generated images" ON storage.objects
FOR SELECT USING (
    bucket_id = 'generated-headshots'
    AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "System can insert generated images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'generated-headshots');

CREATE POLICY "System can delete expired images" ON storage.objects
FOR DELETE USING (bucket_id = 'generated-headshots');

-- =============================================
-- AUTOMATIC CLEANUP FUNCTIONS
-- =============================================

-- Function to get retention period based on user tier
CREATE OR REPLACE FUNCTION get_retention_period(user_tier TEXT)
RETURNS INTERVAL AS $$
BEGIN
    CASE user_tier
        WHEN 'free' THEN RETURN INTERVAL '24 hours';
        WHEN 'starter' THEN RETURN INTERVAL '30 days';
        WHEN 'pro' THEN RETURN INTERVAL '90 days';
        ELSE RETURN INTERVAL '24 hours'; -- Default to free tier
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup expired generations and storage files
CREATE OR REPLACE FUNCTION cleanup_expired_generations()
RETURNS INTEGER AS $$
DECLARE
    expired_generation RECORD;
    deleted_count INTEGER := 0;
    storage_path TEXT;
BEGIN
    -- Find expired generations based on user tier and retention period
    FOR expired_generation IN
        SELECT
            g.id,
            g.user_id,
            g.input_image_url,
            g.output_image_url,
            uc.subscription_tier,
            g.created_at
        FROM generations g
        JOIN user_credits uc ON g.user_id = uc.user_id
        WHERE g.created_at < (NOW() - get_retention_period(uc.subscription_tier))
        AND g.status = 'completed'
    LOOP
        -- Delete input image from storage if it exists
        IF expired_generation.input_image_url LIKE '%supabase%' THEN
            storage_path := regexp_replace(
                expired_generation.input_image_url,
                '.*\/storage\/v1\/object\/public\/[^\/]+\/',
                ''
            );

            DELETE FROM storage.objects
            WHERE bucket_id = 'user-uploads'
            AND name = storage_path;
        END IF;

        -- Delete output image from storage if it exists
        IF expired_generation.output_image_url LIKE '%supabase%' THEN
            storage_path := regexp_replace(
                expired_generation.output_image_url,
                '.*\/storage\/v1\/object\/public\/[^\/]+\/',
                ''
            );

            DELETE FROM storage.objects
            WHERE bucket_id = 'generated-headshots'
            AND name = storage_path;
        END IF;

        -- Delete the generation record
        DELETE FROM generations WHERE id = expired_generation.id;

        deleted_count := deleted_count + 1;

        -- Log the cleanup
        RAISE NOTICE 'Cleaned up generation % for user % (tier: %)',
            expired_generation.id,
            expired_generation.user_id,
            expired_generation.subscription_tier;
    END LOOP;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup orphaned storage files
CREATE OR REPLACE FUNCTION cleanup_orphaned_files()
RETURNS INTEGER AS $$
DECLARE
    orphaned_file RECORD;
    deleted_count INTEGER := 0;
    file_user_id TEXT;
BEGIN
    -- Cleanup orphaned files in user-uploads bucket
    FOR orphaned_file IN
        SELECT name FROM storage.objects
        WHERE bucket_id = 'user-uploads'
        AND created_at < NOW() - INTERVAL '7 days'
    LOOP
        file_user_id := (storage.foldername(orphaned_file.name))[1];

        -- Check if file is referenced in any active generation
        IF NOT EXISTS (
            SELECT 1 FROM generations
            WHERE input_image_url LIKE '%' || orphaned_file.name || '%'
            AND created_at > NOW() - INTERVAL '7 days'
        ) THEN
            DELETE FROM storage.objects
            WHERE bucket_id = 'user-uploads'
            AND name = orphaned_file.name;

            deleted_count := deleted_count + 1;
        END IF;
    END LOOP;

    -- Cleanup orphaned files in generated-headshots bucket
    FOR orphaned_file IN
        SELECT name FROM storage.objects
        WHERE bucket_id = 'generated-headshots'
        AND created_at < NOW() - INTERVAL '7 days'
    LOOP
        -- Check if file is referenced in any active generation
        IF NOT EXISTS (
            SELECT 1 FROM generations
            WHERE output_image_url LIKE '%' || orphaned_file.name || '%'
            AND created_at > NOW() - INTERVAL '7 days'
        ) THEN
            DELETE FROM storage.objects
            WHERE bucket_id = 'generated-headshots'
            AND name = orphaned_file.name;

            deleted_count := deleted_count + 1;
        END IF;
    END LOOP;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get storage usage for a user
CREATE OR REPLACE FUNCTION get_user_storage_usage(user_uuid UUID)
RETURNS TABLE(
    total_files INTEGER,
    total_size_bytes BIGINT,
    input_files INTEGER,
    output_files INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_files,
        COALESCE(SUM(metadata->>'size')::BIGINT, 0) as total_size_bytes,
        COUNT(*) FILTER (WHERE bucket_id = 'user-uploads')::INTEGER as input_files,
        COUNT(*) FILTER (WHERE bucket_id = 'generated-headshots')::INTEGER as output_files
    FROM storage.objects
    WHERE user_uuid::text = (storage.foldername(name))[1];
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- SCHEDULED CLEANUP (CRON JOBS)
-- =============================================

-- Enable pg_cron extension (run as superuser)
-- CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Schedule cleanup to run daily at 2 AM UTC
-- SELECT cron.schedule('cleanup-expired-generations', '0 2 * * *', 'SELECT cleanup_expired_generations();');
-- SELECT cron.schedule('cleanup-orphaned-files', '0 3 * * *', 'SELECT cleanup_orphaned_files();');

-- Manual cleanup commands (run these periodically if cron is not available)
-- SELECT cleanup_expired_generations();
-- SELECT cleanup_orphaned_files();

-- =============================================
-- STORAGE HELPER FUNCTIONS
-- =============================================

-- Function to generate storage path for user files
CREATE OR REPLACE FUNCTION generate_storage_path(user_uuid UUID, file_type TEXT, file_extension TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN user_uuid::text || '/' || file_type || '/' ||
           extract(epoch from now())::bigint || '_' ||
           substr(md5(random()::text), 1, 8) || '.' || file_extension;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has storage quota available
CREATE OR REPLACE FUNCTION check_storage_quota(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_tier TEXT;
    current_usage BIGINT;
    quota_limit BIGINT;
BEGIN
    -- Get user tier
    SELECT subscription_tier INTO user_tier
    FROM user_credits
    WHERE user_id = user_uuid;

    -- Set quota limits (in bytes)
    CASE user_tier
        WHEN 'free' THEN quota_limit := 100 * 1024 * 1024; -- 100MB
        WHEN 'starter' THEN quota_limit := 500 * 1024 * 1024; -- 500MB
        WHEN 'pro' THEN quota_limit := 2 * 1024 * 1024 * 1024; -- 2GB
        ELSE quota_limit := 100 * 1024 * 1024; -- Default 100MB
    END CASE;

    -- Get current usage
    SELECT total_size_bytes INTO current_usage
    FROM get_user_storage_usage(user_uuid);

    RETURN COALESCE(current_usage, 0) < quota_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
