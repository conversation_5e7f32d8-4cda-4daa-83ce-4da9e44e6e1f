import Replicate from 'replicate'
import { prepareApiCall, calculateCost } from '../lib/replicate.js'
import { db, storage } from '../lib/supabase.js'

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
})

// Check if current time is peak hours (9 AM - 6 PM UTC)
const isPeakHours = () => {
  const now = new Date()
  const hour = now.getUTCHours()
  return hour >= 9 && hour <= 18
}

export const generateHeadshot = async (req, res) => {
  try {
    const { styleId, imageUrl, userId, gender } = req.body

    if (!styleId || !imageUrl || !userId) {
      return res.status(400).json({
        error: 'Missing required fields: styleId, imageUrl, userId'
      })
    }

    // Check user tier for priority processing
    const { data: userCredits } = await db.supabase
      .from('user_credits')
      .select('subscription_tier')
      .eq('user_id', userId)
      .single()

    const isPriorityUser = userCredits?.subscription_tier === 'starter' || userCredits?.subscription_tier === 'pro'

    // Prepare the API call based on style
    const apiCall = prepareApiCall(styleId, imageUrl, { gender })
    const cost = calculateCost(styleId)

    console.log('Generating headshot with:', {
      styleId,
      model: apiCall.model,
      cost,
      userId,
      priority: isPriorityUser ? 'HIGH' : 'NORMAL'
    })

    // Add priority processing delay for free users during peak hours
    if (!isPriorityUser && isPeakHours()) {
      console.log('Adding delay for free user during peak hours')
      await new Promise(resolve => setTimeout(resolve, 5000)) // 5 second delay
    }

    // Call Replicate API
    const output = await replicate.run(apiCall.model, { input: apiCall.input })

    // Handle different output formats
    let replicateImageUrl
    if (Array.isArray(output)) {
      replicateImageUrl = output[0]
    } else if (typeof output === 'string') {
      replicateImageUrl = output
    } else {
      throw new Error('Unexpected output format from Replicate')
    }

    // Download the generated image
    const imageBlob = await storage.downloadImage(replicateImageUrl)
    if (imageBlob.error) {
      throw new Error(`Failed to download generated image: ${imageBlob.error}`)
    }

    // Create generation record first to get ID
    const generationId = crypto.randomUUID()

    // Upload generated image to Supabase Storage
    const { data: storageData, error: storageError } = await storage.uploadGeneratedImage(
      imageBlob,
      userId,
      generationId
    )

    if (storageError) {
      console.error('Storage upload error:', storageError)
      // Fallback to Replicate URL if storage fails
      var finalImageUrl = replicateImageUrl
    } else {
      var finalImageUrl = storageData.publicUrl
    }

    // Save generation to database
    const generation = {
      id: generationId,
      user_id: userId,
      style_id: styleId,
      input_image_url: imageUrl,
      output_image_url: finalImageUrl,
      replicate_prediction_id: null, // Would be set for async generations
      status: 'completed',
      cost: cost,
      gender: gender,
      created_at: new Date().toISOString(),
      completed_at: new Date().toISOString()
    }

    const { error: dbError } = await db.saveGeneration(generation)
    if (dbError) {
      console.error('Database save error:', dbError)
      // Continue anyway, user still gets their image
    }

    // Update style analytics
    try {
      await db.supabase.rpc('update_style_analytics', { style_id_param: styleId })
    } catch (analyticsError) {
      console.error('Analytics update error:', analyticsError)
    }

    console.log('Generation completed:', generation)

    res.json({
      success: true,
      generation: {
        id: generationId,
        imageUrl: finalImageUrl,
        styleId,
        cost,
        createdAt: generation.created_at
      }
    })

  } catch (error) {
    console.error('Generation error:', error)
    res.status(500).json({ 
      error: 'Failed to generate headshot',
      details: error.message 
    })
  }
}

export const getGenerationStatus = async (req, res) => {
  try {
    const { predictionId } = req.params

    const prediction = await replicate.predictions.get(predictionId)

    res.json({
      success: true,
      status: prediction.status,
      output: prediction.output,
      error: prediction.error
    })

  } catch (error) {
    console.error('Status check error:', error)
    res.status(500).json({ 
      error: 'Failed to check generation status',
      details: error.message 
    })
  }
}

// Webhook handler for Replicate callbacks
export const handleWebhook = async (req, res) => {
  try {
    const { id, status, output, error } = req.body

    console.log('Webhook received:', { id, status })

    // TODO: Update generation status in database
    if (status === 'succeeded' && output) {
      console.log('Generation succeeded:', output)
    } else if (status === 'failed' && error) {
      console.error('Generation failed:', error)
    }

    res.json({ success: true })

  } catch (error) {
    console.error('Webhook error:', error)
    res.status(500).json({ 
      error: 'Webhook processing failed',
      details: error.message 
    })
  }
}
