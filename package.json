{"name": "headgenius-pro", "private": true, "version": "1.0.0", "type": "module", "description": "The world's best AI headshot generation platform with premium UI/UX", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js", "dev:full": "concurrently \"npm run dev\" \"npm run server\""}, "dependencies": {"@stripe/stripe-js": "^2.2.2", "@supabase/supabase-js": "^2.38.4", "clsx": "^2.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^10.16.16", "gsap": "^3.13.0", "lucide-react": "^0.294.0", "multer": "^1.4.5-lts.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "replicate": "^0.25.2", "stripe": "^14.9.0", "tailwind-merge": "^2.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}}