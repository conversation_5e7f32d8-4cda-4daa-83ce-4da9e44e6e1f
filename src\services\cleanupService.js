// Cleanup Service for HeadGenius Pro
// Handles automatic deletion of expired files based on user tier

import { supabase } from '../lib/supabase.js'

export class CleanupService {
  constructor() {
    this.isRunning = false
    this.intervalId = null
  }

  // Start the cleanup service (runs every hour)
  start() {
    if (this.isRunning) {
      console.log('Cleanup service is already running')
      return
    }

    console.log('Starting cleanup service...')
    this.isRunning = true
    
    // Run immediately
    this.runCleanup()
    
    // Then run every hour
    this.intervalId = setInterval(() => {
      this.runCleanup()
    }, 60 * 60 * 1000) // 1 hour
  }

  // Stop the cleanup service
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    this.isRunning = false
    console.log('Cleanup service stopped')
  }

  // Run the cleanup process
  async runCleanup() {
    try {
      console.log('Running cleanup process...')
      
      const expiredCount = await this.cleanupExpiredGenerations()
      const orphanedCount = await this.cleanupOrphanedFiles()
      
      console.log(`Cleanup completed: ${expiredCount} expired generations, ${orphanedCount} orphaned files`)
      
      // Log cleanup stats
      await this.logCleanupStats(expiredCount, orphanedCount)
      
    } catch (error) {
      console.error('Cleanup process failed:', error)
    }
  }

  // Cleanup expired generations based on user tier retention periods
  async cleanupExpiredGenerations() {
    try {
      const { data, error } = await supabase.rpc('cleanup_expired_generations')
      
      if (error) {
        console.error('Database cleanup error:', error)
        return 0
      }
      
      return data || 0
    } catch (error) {
      console.error('Error calling cleanup function:', error)
      return 0
    }
  }

  // Cleanup orphaned files that are no longer referenced
  async cleanupOrphanedFiles() {
    try {
      const { data, error } = await supabase.rpc('cleanup_orphaned_files')
      
      if (error) {
        console.error('Orphaned files cleanup error:', error)
        return 0
      }
      
      return data || 0
    } catch (error) {
      console.error('Error calling orphaned cleanup function:', error)
      return 0
    }
  }

  // Log cleanup statistics
  async logCleanupStats(expiredCount, orphanedCount) {
    try {
      // You could save this to a cleanup_logs table if needed
      console.log('Cleanup stats:', {
        timestamp: new Date().toISOString(),
        expired_generations: expiredCount,
        orphaned_files: orphanedCount
      })
    } catch (error) {
      console.error('Error logging cleanup stats:', error)
    }
  }

  // Get retention period for a user tier
  getRetentionPeriod(tier) {
    switch (tier) {
      case 'free':
        return 24 * 60 * 60 * 1000 // 24 hours in milliseconds
      case 'starter':
        return 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
      case 'pro':
        return 90 * 24 * 60 * 60 * 1000 // 90 days in milliseconds
      default:
        return 24 * 60 * 60 * 1000 // Default to free tier
    }
  }

  // Check if a generation is expired
  isGenerationExpired(createdAt, userTier) {
    const retentionPeriod = this.getRetentionPeriod(userTier)
    const createdTime = new Date(createdAt).getTime()
    const now = Date.now()
    
    return (now - createdTime) > retentionPeriod
  }

  // Get storage usage for all users
  async getStorageStats() {
    try {
      const { data: users, error } = await supabase
        .from('user_profiles')
        .select('user_id, subscription_tier')
      
      if (error) throw error
      
      const stats = {
        total_users: users.length,
        free_users: 0,
        starter_users: 0,
        pro_users: 0,
        total_storage: 0
      }
      
      for (const user of users) {
        stats[`${user.subscription_tier}_users`]++
        
        const { data: usage } = await supabase
          .rpc('get_user_storage_usage', { user_uuid: user.user_id })
        
        if (usage && usage[0]) {
          stats.total_storage += usage[0].total_size_bytes || 0
        }
      }
      
      return stats
    } catch (error) {
      console.error('Error getting storage stats:', error)
      return null
    }
  }

  // Manual cleanup for specific user
  async cleanupUserFiles(userId) {
    try {
      // Get user's tier to determine retention
      const { data: userCredits } = await supabase
        .from('user_credits')
        .select('subscription_tier')
        .eq('user_id', userId)
        .single()
      
      if (!userCredits) {
        throw new Error('User not found')
      }
      
      const retentionPeriod = this.getRetentionPeriod(userCredits.subscription_tier)
      const cutoffDate = new Date(Date.now() - retentionPeriod)
      
      // Get expired generations for this user
      const { data: expiredGenerations } = await supabase
        .from('generations')
        .select('*')
        .eq('user_id', userId)
        .lt('created_at', cutoffDate.toISOString())
      
      let deletedCount = 0
      
      for (const generation of expiredGenerations || []) {
        // Delete storage files
        if (generation.input_image_url?.includes('supabase')) {
          await this.deleteStorageFile(generation.input_image_url, 'user-uploads')
        }
        if (generation.output_image_url?.includes('supabase')) {
          await this.deleteStorageFile(generation.output_image_url, 'generated-headshots')
        }
        
        // Delete generation record
        await supabase
          .from('generations')
          .delete()
          .eq('id', generation.id)
        
        deletedCount++
      }
      
      return deletedCount
    } catch (error) {
      console.error('Error cleaning up user files:', error)
      throw error
    }
  }

  // Helper to delete storage file from URL
  async deleteStorageFile(url, bucket) {
    try {
      // Extract file path from URL
      const urlParts = url.split('/')
      const bucketIndex = urlParts.findIndex(part => part === bucket)
      if (bucketIndex === -1) return
      
      const filePath = urlParts.slice(bucketIndex + 1).join('/')
      
      await supabase.storage
        .from(bucket)
        .remove([filePath])
        
    } catch (error) {
      console.error('Error deleting storage file:', error)
    }
  }
}

// Create singleton instance
export const cleanupService = new CleanupService()

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  cleanupService.start()
}

// Export retention periods for use in UI
export const RETENTION_PERIODS = {
  free: '24 hours',
  starter: '30 days', 
  pro: '90 days'
}
