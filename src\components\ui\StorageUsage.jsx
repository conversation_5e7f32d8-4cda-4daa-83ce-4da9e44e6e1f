import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { HardDrive, Trash2, Clock, Info } from 'lucide-react'
import { storage } from '../../lib/supabase'
import { RETENTION_PERIODS } from '../../services/cleanupService'
import { useAuthStore } from '../../store/authStore'

const StorageUsage = () => {
  const [storageData, setStorageData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const { user, credits } = useAuthStore()

  useEffect(() => {
    if (user) {
      loadStorageData()
    }
  }, [user])

  const loadStorageData = async () => {
    try {
      setLoading(true)
      const { data, error } = await storage.getStorageUsage(user.id)
      
      if (error) throw error
      
      setStorageData(data)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStorageQuota = (tier) => {
    switch (tier) {
      case 'free':
        return 100 * 1024 * 1024 // 100MB
      case 'starter':
        return 500 * 1024 * 1024 // 500MB
      case 'pro':
        return 2 * 1024 * 1024 * 1024 // 2GB
      default:
        return 100 * 1024 * 1024
    }
  }

  const quota = getStorageQuota(credits.tier)
  const usagePercentage = storageData ? (storageData.total_size_bytes / quota) * 100 : 0

  if (loading) {
    return (
      <div className="card">
        <div className="animate-pulse">
          <div className="h-4 bg-secondary-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-secondary-200 rounded w-1/2 mb-2"></div>
          <div className="h-2 bg-secondary-200 rounded w-full"></div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="card bg-red-50 border-red-200">
        <div className="flex items-center gap-2 text-red-600">
          <Info className="w-4 h-4" />
          <span className="text-sm">Failed to load storage data</span>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="card"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-secondary-900 flex items-center gap-2">
          <HardDrive className="w-5 h-5" />
          Storage Usage
        </h3>
        <button
          onClick={loadStorageData}
          className="text-sm text-primary-600 hover:text-primary-700"
        >
          Refresh
        </button>
      </div>

      {/* Usage Stats */}
      <div className="space-y-4">
        <div>
          <div className="flex justify-between text-sm mb-2">
            <span className="text-secondary-600">
              {formatBytes(storageData?.total_size_bytes || 0)} of {formatBytes(quota)} used
            </span>
            <span className="text-secondary-600">
              {usagePercentage.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-secondary-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                usagePercentage > 90 ? 'bg-red-500' :
                usagePercentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            ></div>
          </div>
        </div>

        {/* File Breakdown */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="text-secondary-600 mb-1">Input Images</div>
            <div className="font-semibold text-secondary-900">
              {storageData?.input_files || 0} files
            </div>
          </div>
          <div className="bg-secondary-50 rounded-lg p-3">
            <div className="text-secondary-600 mb-1">Generated Images</div>
            <div className="font-semibold text-secondary-900">
              {storageData?.output_files || 0} files
            </div>
          </div>
        </div>

        {/* Retention Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <Clock className="w-4 h-4 text-blue-600 mt-0.5" />
            <div className="text-sm">
              <div className="font-medium text-blue-900 mb-1">
                File Retention: {RETENTION_PERIODS[credits.tier]}
              </div>
              <div className="text-blue-700">
                {credits.tier === 'free' && 'Files are automatically deleted after 24 hours'}
                {credits.tier === 'starter' && 'Files are kept for 30 days, then automatically deleted'}
                {credits.tier === 'pro' && 'Files are kept for 90 days with extended retention'}
              </div>
            </div>
          </div>
        </div>

        {/* Storage Warning */}
        {usagePercentage > 80 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Info className="w-4 h-4 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <div className="font-medium text-yellow-900 mb-1">
                  Storage Almost Full
                </div>
                <div className="text-yellow-700">
                  Consider upgrading your plan or wait for older files to be automatically cleaned up.
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Upgrade Prompt for Free Users */}
        {credits.tier === 'free' && usagePercentage > 50 && (
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-3">
            <div className="text-sm">
              <div className="font-medium text-primary-900 mb-1">
                Need More Storage?
              </div>
              <div className="text-primary-700 mb-2">
                Upgrade to Starter (500MB) or Pro (2GB) for more storage and longer retention.
              </div>
              <a
                href="/pricing"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                View Plans →
              </a>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
}

export default StorageUsage
