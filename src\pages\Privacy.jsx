import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Shield, Eye, Lock, Trash2, Download, UserCheck, Clock, Globe } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const Privacy = () => {
  const heroRef = useRef(null)
  const contentRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.privacy-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.privacy-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating shield animation
      gsap.to('.floating-shield', {
        y: -20,
        rotation: 5,
        duration: 3,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1
      })

      // Content sections animation
      gsap.fromTo('.privacy-section', 
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: contentRef.current,
            start: 'top 80%'
          }
        }
      )

      // Highlight boxes animation
      gsap.fromTo('.highlight-box', 
        { scale: 0.9, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: '.highlight-container',
            start: 'top 80%'
          }
        }
      )
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const highlights = [
    {
      icon: Shield,
      title: 'Data Protection',
      description: 'Enterprise-grade encryption for all your data'
    },
    {
      icon: Trash2,
      title: 'Auto-Deletion',
      description: 'Images automatically deleted per retention policy'
    },
    {
      icon: Lock,
      title: 'Secure Processing',
      description: 'All AI processing happens on secure servers'
    },
    {
      icon: UserCheck,
      title: 'No Third Parties',
      description: 'We never share your photos with anyone'
    }
  ]

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="floating-shield absolute top-10 right-10 text-primary-400 opacity-20">
            <Shield className="w-20 h-20" />
          </div>
          
          <h1 className="privacy-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            Privacy <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Policy</span>
          </h1>
          <p className="privacy-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Your privacy is our priority. Learn how we protect and handle your data.
          </p>
          <div className="mt-8 text-sm text-secondary-500">
            Last updated: January 15, 2025
          </div>
        </div>
      </section>

      {/* Highlights */}
      <section className="py-16 highlight-container">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-6">
            {highlights.map((item, index) => (
              <motion.div
                key={index}
                className="highlight-box card text-center group hover:shadow-lg transition-all duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-accent-600 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <item.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="font-bold text-secondary-900 mb-2">{item.title}</h3>
                <p className="text-sm text-secondary-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-16" ref={contentRef}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto space-y-12">
            
            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Eye className="w-6 h-6 text-primary-600" />
                Information We Collect
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">We collect information you provide directly to us, such as:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Account Information:</strong> Email address, name, and profile details</li>
                  <li><strong>Photos:</strong> Images you upload for headshot generation</li>
                  <li><strong>Payment Information:</strong> Billing details processed securely through Stripe</li>
                  <li><strong>Usage Data:</strong> How you interact with our platform for improvement purposes</li>
                </ul>
                <p>We automatically collect certain information when you use our service, including device information, IP address, and usage patterns to improve our service and ensure security.</p>
              </div>
            </div>

            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Lock className="w-6 h-6 text-primary-600" />
                How We Use Your Information
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">We use the information we collect to:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Generate AI-powered headshots using your uploaded photos</li>
                  <li>Provide, maintain, and improve our services</li>
                  <li>Process payments and manage your subscription</li>
                  <li>Send you technical notices and support messages</li>
                  <li>Respond to your comments and questions</li>
                  <li>Analyze usage patterns to enhance user experience</li>
                </ul>
                <p><strong>Important:</strong> Your photos are used solely for generating your headshots and are never used to train our AI models or shared with third parties.</p>
              </div>
            </div>

            <div className="privacy-section card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Clock className="w-6 h-6 text-primary-600" />
                Data Retention Policy
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">We automatically delete your images based on your subscription tier:</p>
                <div className="grid md:grid-cols-3 gap-4 mb-4">
                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Free Tier</h4>
                    <p className="text-sm">Images deleted after <strong>24 hours</strong></p>
                  </div>
                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Starter Pack</h4>
                    <p className="text-sm">Images deleted after <strong>30 days</strong></p>
                  </div>
                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Pro Pack</h4>
                    <p className="text-sm">Images deleted after <strong>90 days</strong></p>
                  </div>
                </div>
                <p>Account information is retained until you delete your account. You can request immediate deletion of your data at any time.</p>
              </div>
            </div>

            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Shield className="w-6 h-6 text-primary-600" />
                Data Security
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">We implement industry-standard security measures to protect your data:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>End-to-end encryption for all data transmission</li>
                  <li>Secure cloud storage with enterprise-grade protection</li>
                  <li>Regular security audits and vulnerability assessments</li>
                  <li>Access controls and authentication protocols</li>
                  <li>Secure payment processing through Stripe (PCI DSS compliant)</li>
                </ul>
                <p>While we strive to protect your information, no method of transmission over the internet is 100% secure. We continuously work to improve our security measures.</p>
              </div>
            </div>

            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <UserCheck className="w-6 h-6 text-primary-600" />
                Your Rights
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">You have the following rights regarding your personal data:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Access:</strong> Request a copy of your personal data</li>
                  <li><strong>Correction:</strong> Update or correct inaccurate information</li>
                  <li><strong>Deletion:</strong> Request deletion of your personal data</li>
                  <li><strong>Portability:</strong> Export your data in a machine-readable format</li>
                  <li><strong>Objection:</strong> Object to certain processing of your data</li>
                </ul>
                <p>To exercise these rights, contact <NAME_EMAIL>. We will respond within 30 days.</p>
              </div>
            </div>

            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <Globe className="w-6 h-6 text-primary-600" />
                International Transfers
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  HeadGenius Pro operates globally. Your information may be transferred to and processed in countries other than your own. 
                  We ensure appropriate safeguards are in place to protect your data in accordance with this privacy policy.
                </p>
                <p>
                  For users in the European Union, we comply with GDPR requirements and ensure adequate protection for international data transfers.
                </p>
              </div>
            </div>

            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Third-Party Services</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">We use the following third-party services:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Stripe:</strong> Payment processing (subject to Stripe's privacy policy)</li>
                  <li><strong>Supabase:</strong> Database and authentication services</li>
                  <li><strong>Replicate:</strong> AI model hosting and processing</li>
                  <li><strong>Google Analytics:</strong> Website analytics (anonymized data)</li>
                </ul>
                <p>These services have their own privacy policies and data handling practices. We encourage you to review them.</p>
              </div>
            </div>

            <div className="privacy-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Changes to This Policy</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page 
                  and updating the "Last updated" date. For significant changes, we may also send you an email notification.
                </p>
                <p>
                  Your continued use of HeadGenius Pro after any changes indicates your acceptance of the updated privacy policy.
                </p>
              </div>
            </div>

            <div className="privacy-section card bg-secondary-900 text-white">
              <h2 className="text-2xl font-bold mb-4">Contact Us</h2>
              <div className="prose prose-lg text-secondary-300">
                <p className="mb-4">
                  If you have any questions about this privacy policy or our data practices, please contact us:
                </p>
                <ul className="list-none space-y-2">
                  <li><strong>Email:</strong> <EMAIL></li>
                  <li><strong>Support:</strong> <EMAIL></li>
                  <li><strong>Address:</strong> San Francisco, CA, United States</li>
                </ul>
              </div>
            </div>

          </div>
        </div>
      </section>
    </div>
  )
}

export default Privacy
