import { useEffect } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Camera, CreditCard, Clock, Star, ArrowRight, Plus } from 'lucide-react'
import { useAuthStore } from '../store/authStore'
import StorageUsage from '../components/ui/StorageUsage'

const Dashboard = () => {
  const { user, credits, canUseFreeCredit } = useAuthStore()
  const freeCredits = canUseFreeCredit()

  if (!user) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">Please sign in</h1>
          <Link to="/auth" className="btn-primary">
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen gradient-bg py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-secondary-900 mb-2">
            Welcome back, {user.email?.split('@')[0]}!
          </h1>
          <p className="text-secondary-600">
            Ready to create some amazing headshots?
          </p>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="card"
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <CreditCard className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-secondary-900">Credits</h3>
                <p className="text-2xl font-bold text-primary-600">{credits.remaining}</p>
                <p className="text-sm text-secondary-500">Available</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="card"
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Camera className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-secondary-900">Free Credits</h3>
                <p className="text-2xl font-bold text-green-600">{freeCredits.remaining}</p>
                <p className="text-sm text-secondary-500">This period</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="card"
          >
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-accent-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-secondary-900">Plan</h3>
                <p className="text-2xl font-bold text-accent-600 capitalize">{credits.tier}</p>
                <p className="text-sm text-secondary-500">Current tier</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Quick Actions & Storage */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid md:grid-cols-2 gap-6 mb-8"
        >
          <div className="card">
            <h3 className="text-xl font-semibold text-secondary-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Link
                to="/generate"
                className="flex items-center justify-between p-4 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors duration-200"
              >
                <div className="flex items-center gap-3">
                  <Camera className="w-5 h-5 text-primary-600" />
                  <span className="font-medium text-primary-900">Generate New Headshot</span>
                </div>
                <ArrowRight className="w-5 h-5 text-primary-600" />
              </Link>
              
              <Link
                to="/gallery"
                className="flex items-center justify-between p-4 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors duration-200"
              >
                <div className="flex items-center gap-3">
                  <Star className="w-5 h-5 text-secondary-600" />
                  <span className="font-medium text-secondary-900">View Gallery</span>
                </div>
                <ArrowRight className="w-5 h-5 text-secondary-600" />
              </Link>

              {credits.tier === 'free' && (
                <Link
                  to="/pricing"
                  className="flex items-center justify-between p-4 bg-accent-50 rounded-lg hover:bg-accent-100 transition-colors duration-200"
                >
                  <div className="flex items-center gap-3">
                    <Plus className="w-5 h-5 text-accent-600" />
                    <span className="font-medium text-accent-900">Upgrade Plan</span>
                  </div>
                  <ArrowRight className="w-5 h-5 text-accent-600" />
                </Link>
              )}
            </div>
          </div>

          <StorageUsage />
        </motion.div>

        {/* Free Tier Notice */}
        {credits.tier === 'free' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200"
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-secondary-900 mb-2">
                  Unlock More with Pro
                </h3>
                <p className="text-secondary-600 mb-4">
                  Get unlimited headshots, priority processing, and advanced features.
                </p>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• 40+ headshots with Starter Pack ($19)</li>
                  <li>• 100+ headshots with Pro Pack ($35)</li>
                  <li>• All style categories included</li>
                  <li>• No watermarks, longer retention</li>
                </ul>
              </div>
              <div className="ml-6">
                <Link
                  to="/pricing"
                  className="btn-primary whitespace-nowrap"
                >
                  Upgrade Now
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}

export default Dashboard
