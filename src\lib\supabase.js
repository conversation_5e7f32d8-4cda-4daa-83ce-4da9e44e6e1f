import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Auth helpers
export const auth = {
  signUp: async (email, password) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    return { data, error }
  },

  signIn: async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  },

  signInWithGoogle: async () => {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`
      }
    })
    return { data, error }
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser()
    return { user, error }
  },

  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Database helpers
export const db = {
  // User credits management
  getUserCredits: async (userId) => {
    const { data, error } = await supabase
      .from('user_credits')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    return { data, error }
  },

  updateUserCredits: async (userId, credits) => {
    const { data, error } = await supabase
      .from('user_credits')
      .upsert({
        user_id: userId,
        total_credits: credits.total,
        used_credits: credits.used,
        free_credits_used: credits.freeUsed,
        last_free_reset: credits.lastReset,
        subscription_tier: credits.tier
      })
      .select()
    
    return { data, error }
  },

  // Generation history
  saveGeneration: async (generation) => {
    const { data, error } = await supabase
      .from('generations')
      .insert(generation)
      .select()
    
    return { data, error }
  },

  getUserGenerations: async (userId) => {
    const { data, error } = await supabase
      .from('generations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    return { data, error }
  },

  // Payment tracking
  savePayment: async (payment) => {
    const { data, error } = await supabase
      .from('payments')
      .insert(payment)
      .select()
    
    return { data, error }
  }
}

// Storage helpers
export const storage = {
  // Upload user input image
  uploadInputImage: async (file, userId) => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}/inputs/${Date.now()}_${Math.random().toString(36).substring(7)}.${fileExt}`

    const { data, error } = await supabase.storage
      .from('user-uploads')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) return { data: null, error }

    const { data: urlData } = supabase.storage
      .from('user-uploads')
      .getPublicUrl(fileName)

    return { data: { ...data, publicUrl: urlData.publicUrl }, error: null }
  },

  // Upload generated headshot
  uploadGeneratedImage: async (imageBlob, userId, generationId) => {
    const fileName = `${userId}/outputs/${generationId}_${Date.now()}.webp`

    const { data, error } = await supabase.storage
      .from('generated-headshots')
      .upload(fileName, imageBlob, {
        cacheControl: '3600',
        upsert: false,
        contentType: 'image/webp'
      })

    if (error) return { data: null, error }

    const { data: urlData } = supabase.storage
      .from('generated-headshots')
      .getPublicUrl(fileName)

    return { data: { ...data, publicUrl: urlData.publicUrl }, error: null }
  },

  // Get public URL for any storage file
  getPublicUrl: (bucket, path) => {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path)

    return data.publicUrl
  },

  // Delete file from storage
  deleteFile: async (bucket, path) => {
    const { data, error } = await supabase.storage
      .from(bucket)
      .remove([path])

    return { data, error }
  },

  // Check user storage quota
  checkStorageQuota: async (userId) => {
    const { data, error } = await supabase
      .rpc('check_storage_quota', { user_uuid: userId })

    return { hasQuota: data, error }
  },

  // Get user storage usage
  getStorageUsage: async (userId) => {
    const { data, error } = await supabase
      .rpc('get_user_storage_usage', { user_uuid: userId })

    return { data: data?.[0] || null, error }
  },

  // Download image as blob (for processing)
  downloadImage: async (url) => {
    try {
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to download image')
      return await response.blob()
    } catch (error) {
      return { error: error.message }
    }
  }
}
