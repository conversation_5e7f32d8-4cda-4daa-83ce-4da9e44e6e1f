import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { DollarSign, Clock, CheckCircle, XCircle, AlertCircle, MessageCircle, CreditCard, RefreshCw } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const Refund = () => {
  const heroRef = useRef(null)
  const contentRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.refund-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.refund-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating money icons
      gsap.to('.floating-money', {
        y: -20,
        rotation: 10,
        duration: 3,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.5
      })

      // Content sections animation
      gsap.fromTo('.refund-section', 
        { x: -50, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: contentRef.current,
            start: 'top 80%'
          }
        }
      )

      // Policy cards animation
      gsap.fromTo('.policy-card', 
        { scale: 0.9, opacity: 0 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.6,
          stagger: 0.15,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: '.policy-container',
            start: 'top 80%'
          }
        }
      )
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const refundPolicies = [
    {
      icon: CheckCircle,
      title: 'Eligible for Refund',
      color: 'green',
      items: [
        'Technical issues preventing service use',
        'Billing errors or duplicate charges',
        'Service not delivered as promised',
        'Account issues within first 7 days'
      ]
    },
    {
      icon: XCircle,
      title: 'Not Eligible for Refund',
      color: 'red',
      items: [
        'Change of mind after using the service',
        'Dissatisfaction with AI-generated results',
        'Partial month usage after cancellation',
        'Violation of terms of service'
      ]
    },
    {
      icon: Clock,
      title: 'Time Limits',
      color: 'blue',
      items: [
        '7 days for new subscription refunds',
        '30 days for billing error disputes',
        '24 hours for duplicate charge reports',
        'No refunds after 30 days from charge'
      ]
    }
  ]

  const refundProcess = [
    {
      step: 1,
      title: 'Contact Support',
      description: 'Email <NAME_EMAIL> with your refund request and reason',
      icon: MessageCircle
    },
    {
      step: 2,
      title: 'Review Process',
      description: 'Our team reviews your request within 2 business days',
      icon: RefreshCw
    },
    {
      step: 3,
      title: 'Decision & Processing',
      description: 'If approved, refunds are processed within 5-7 business days',
      icon: CreditCard
    }
  ]

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="floating-money absolute top-10 left-10 text-primary-400 opacity-20">
            <DollarSign className="w-16 h-16" />
          </div>
          <div className="floating-money absolute top-20 right-20 text-accent-400 opacity-20">
            <CreditCard className="w-12 h-12" />
          </div>
          <div className="floating-money absolute bottom-20 left-20 text-primary-400 opacity-20">
            <RefreshCw className="w-14 h-14" />
          </div>
          
          <h1 className="refund-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            Refund <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Policy</span>
          </h1>
          <p className="refund-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Clear and fair refund terms for HeadGenius Pro subscriptions and services.
          </p>
          <div className="mt-8 text-sm text-secondary-500">
            Last updated: January 15, 2025
          </div>
        </div>
      </section>

      {/* Quick Overview */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200 text-center"
            >
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-accent-600 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-secondary-900">Our Commitment</h2>
              </div>
              <p className="text-lg text-secondary-700 leading-relaxed">
                We stand behind our service and want you to be completely satisfied. While we have specific refund policies, 
                we're committed to working with you to resolve any issues and ensure you have a positive experience with HeadGenius Pro.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Refund Policies */}
      <section className="py-16 policy-container">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-secondary-900 mb-4">Refund Eligibility</h2>
            <p className="text-secondary-600">Understanding when refunds are available</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {refundPolicies.map((policy, index) => (
              <motion.div
                key={index}
                className="policy-card card"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    policy.color === 'green' ? 'bg-green-100 text-green-600' :
                    policy.color === 'red' ? 'bg-red-100 text-red-600' :
                    'bg-blue-100 text-blue-600'
                  }`}>
                    <policy.icon className="w-5 h-5" />
                  </div>
                  <h3 className="text-xl font-bold text-secondary-900">{policy.title}</h3>
                </div>
                <ul className="space-y-3">
                  {policy.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-3">
                      <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                        policy.color === 'green' ? 'bg-green-500' :
                        policy.color === 'red' ? 'bg-red-500' :
                        'bg-blue-500'
                      }`}></div>
                      <span className="text-secondary-700">{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Refund Process */}
      <section className="py-16 bg-white/50 backdrop-blur-sm">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-secondary-900 mb-4">How to Request a Refund</h2>
            <p className="text-secondary-600">Simple steps to process your refund request</p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-3 gap-8">
              {refundProcess.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-accent-600 rounded-full flex items-center justify-center mx-auto">
                      <step.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-secondary-900 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-secondary-900 mb-3">{step.title}</h3>
                  <p className="text-secondary-600">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Terms */}
      <section className="py-16" ref={contentRef}>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto space-y-12">
            
            <div className="refund-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <CreditCard className="w-6 h-6 text-primary-600" />
                Subscription Refunds
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4"><strong>New Subscriptions:</strong></p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>Full refund available within 7 days of first subscription</li>
                  <li>Must not have exceeded free tier usage limits</li>
                  <li>Account must be in good standing with no violations</li>
                </ul>
                <p className="mb-4"><strong>Recurring Subscriptions:</strong></p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li>No refunds for partial months after service usage</li>
                  <li>Cancellation takes effect at end of billing period</li>
                  <li>Pro-rated refunds only for billing errors</li>
                </ul>
              </div>
            </div>

            <div className="refund-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4 flex items-center gap-3">
                <AlertCircle className="w-6 h-6 text-amber-600" />
                Special Circumstances
              </h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">We may consider refunds outside our standard policy for:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Technical Issues:</strong> Service unavailable for extended periods</li>
                  <li><strong>Billing Errors:</strong> Incorrect charges or duplicate payments</li>
                  <li><strong>Service Failures:</strong> AI generation consistently failing</li>
                  <li><strong>Account Problems:</strong> Issues preventing normal service use</li>
                </ul>
                <p>Each case is reviewed individually, and we reserve the right to make exceptions at our discretion.</p>
              </div>
            </div>

            <div className="refund-section card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Processing Times</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">Refund processing times vary by payment method:</p>
                <div className="grid md:grid-cols-2 gap-4 mb-4">
                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">Credit/Debit Cards</h4>
                    <p className="text-sm">3-5 business days after approval</p>
                  </div>
                  <div className="bg-white p-4 rounded-lg border">
                    <h4 className="font-bold text-secondary-900 mb-2">PayPal</h4>
                    <p className="text-sm">1-2 business days after approval</p>
                  </div>
                </div>
                <p className="text-sm text-secondary-600">
                  <strong>Note:</strong> Processing times may be longer during holidays or due to bank processing delays.
                </p>
              </div>
            </div>

            <div className="refund-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Refund Alternatives</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">Before requesting a refund, consider these alternatives:</p>
                <ul className="list-disc pl-6 mb-4 space-y-2">
                  <li><strong>Account Credit:</strong> We may offer service credits for future use</li>
                  <li><strong>Plan Downgrade:</strong> Switch to a lower-tier plan with pro-rated adjustment</li>
                  <li><strong>Extended Trial:</strong> Additional time to evaluate the service</li>
                  <li><strong>Technical Support:</strong> Help resolving issues that may improve your experience</li>
                </ul>
                <p>Our support team will work with you to find the best solution for your situation.</p>
              </div>
            </div>

            <div className="refund-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Dispute Resolution</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  If you disagree with a refund decision, you can request a review <NAME_EMAIL> 
                  with additional information or documentation.
                </p>
                <p className="mb-4">
                  For payment disputes, you may also contact your credit card company or payment provider. 
                  However, we encourage resolving issues directly with us first.
                </p>
                <p>
                  Chargebacks initiated without first contacting us may result in account suspension pending resolution.
                </p>
              </div>
            </div>

            <div className="refund-section card">
              <h2 className="text-2xl font-bold text-secondary-900 mb-4">Policy Updates</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  This refund policy may be updated from time to time. Changes will be posted on this page with 
                  an updated "Last updated" date.
                </p>
                <p>
                  Significant changes will be communicated via email to active subscribers. Continued use of the 
                  service after policy changes constitutes acceptance of the updated terms.
                </p>
              </div>
            </div>

            <div className="refund-section card bg-secondary-900 text-white">
              <h2 className="text-2xl font-bold mb-4">Contact for Refunds</h2>
              <div className="prose prose-lg text-secondary-300">
                <p className="mb-4">
                  To request a refund or discuss your situation, please contact us:
                </p>
                <ul className="list-none space-y-2">
                  <li><strong>Email:</strong> <EMAIL></li>
                  <li><strong>Subject Line:</strong> "Refund Request - [Your Account Email]"</li>
                  <li><strong>Include:</strong> Reason for refund, order/subscription details, any relevant screenshots</li>
                </ul>
                <p className="mt-4 text-sm">
                  We typically respond to refund requests within 2 business days.
                </p>
              </div>
            </div>

          </div>
        </div>
      </section>
    </div>
  )
}

export default Refund
