// Replicate API configuration and helpers
export const MODELS = {
  PROFESSIONAL: 'flux-kontext-apps/professional-headshot',
  CREATIVE: 'zsxkib/instant-id'
}

export const STYLE_CONFIGS = {
  // Professional styles using flux-kontext-apps/professional-headshot
  'professional-corporate': {
    model: MODELS.PROFESSIONAL,
    config: { background: 'neutral', aspect_ratio: '1:1' },
    cost: 0.04,
    description: 'Classic corporate headshot with neutral background'
  },
  'professional-executive': {
    model: MODELS.PROFESSIONAL,
    config: { background: 'office', aspect_ratio: '1:1' },
    cost: 0.04,
    description: 'Executive-style portrait with office setting'
  },
  'professional-modern': {
    model: MODELS.PROFESSIONAL,
    config: { background: 'gray', aspect_ratio: '1:1' },
    cost: 0.04,
    description: 'Modern business look with gray backdrop'
  },
  'professional-outdoor': {
    model: MODELS.PROFESSIONAL,
    config: { background: 'outdoor', aspect_ratio: '1:1' },
    cost: 0.04,
    description: 'Professional outdoor business portrait'
  },
  'professional-library': {
    model: MODELS.PROFESSIONAL,
    config: { background: 'library', aspect_ratio: '1:1' },
    cost: 0.04,
    description: 'Sophisticated library background setting'
  },

  // Casual & Lifestyle styles using zsxkib/instant-id
  'casual-outdoor': {
    model: MODELS.CREATIVE,
    prompt: 'natural outdoor portrait, golden hour lighting, confident smile, shallow depth of field, professional photography, relaxed casual attire',
    negative_prompt: 'formal attire, suit, tie, office background, artificial lighting, low quality, blurry, indoor',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Natural outdoor portrait with golden hour lighting'
  },
  'casual-coffee': {
    model: MODELS.CREATIVE,
    prompt: 'cozy coffee shop portrait, warm natural lighting, relaxed smile, soft background blur, approachable and friendly, casual smart attire',
    negative_prompt: 'formal suit, tie, harsh lighting, low quality, blurry, artificial, serious expression',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Warm coffee shop atmosphere with natural lighting'
  },
  'casual-beach': {
    model: MODELS.CREATIVE,
    prompt: 'beach lifestyle portrait, natural sunlight, relaxed expression, ocean background, casual summer attire, vacation vibes',
    negative_prompt: 'formal wear, office, indoor, artificial lighting, low quality, blurry, serious',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Relaxed beach lifestyle with ocean backdrop'
  },
  'casual-park': {
    model: MODELS.CREATIVE,
    prompt: 'park setting portrait, natural daylight, friendly smile, green nature background, casual comfortable clothing',
    negative_prompt: 'formal business attire, indoor, artificial lighting, low quality, blurry, serious expression',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Fresh park setting with natural greenery'
  },

  // Dating & Social styles
  'dating-romantic': {
    model: MODELS.CREATIVE,
    prompt: 'romantic portrait, soft warm lighting, genuine smile, attractive and approachable, dating profile photo, natural beauty',
    negative_prompt: 'overly formal, serious expression, poor lighting, blurry, low quality, artificial, business attire',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Romantic and attractive for dating profiles'
  },
  'dating-fun': {
    model: MODELS.CREATIVE,
    prompt: 'fun and playful portrait, bright natural lighting, joyful expression, vibrant personality, social media ready',
    negative_prompt: 'serious, formal, business attire, poor lighting, low quality, blurry, boring',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Fun and playful for social media'
  },
  'dating-elegant': {
    model: MODELS.CREATIVE,
    prompt: 'elegant portrait, sophisticated lighting, confident smile, classy and refined, upscale dating photo',
    negative_prompt: 'casual, messy, poor lighting, low quality, blurry, unprofessional',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Elegant and sophisticated appeal'
  },
  'dating-adventure': {
    model: MODELS.CREATIVE,
    prompt: 'adventurous outdoor portrait, dynamic lighting, confident expression, active lifestyle, travel-ready photo',
    negative_prompt: 'indoor, formal wear, static pose, poor lighting, low quality, blurry, boring',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Adventurous spirit for active daters'
  },

  // Creative & Branding styles
  'creative-artistic': {
    model: MODELS.CREATIVE,
    prompt: 'artistic portrait, dramatic lighting, creative background, professional headshot with personality, artistic flair, unique style',
    negative_prompt: 'boring, plain, corporate, standard headshot, no personality, low quality, conventional',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Artistic flair with dramatic lighting'
  },
  'creative-urban': {
    model: MODELS.CREATIVE,
    prompt: 'urban creative portrait, modern city background, confident expression, contemporary style, professional yet edgy, street art vibes',
    negative_prompt: 'formal corporate, suit, tie, traditional, boring, low quality, blurry, suburban',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Urban edge with city backdrop'
  },
  'creative-studio': {
    model: MODELS.CREATIVE,
    prompt: 'creative studio portrait, professional lighting setup, artistic expression, photographer studio background, creative professional',
    negative_prompt: 'amateur, poor lighting, low quality, blurry, boring, conventional, office setting',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Professional studio with artistic touch'
  },
  'creative-vintage': {
    model: MODELS.CREATIVE,
    prompt: 'vintage inspired portrait, film photography aesthetic, retro styling, classic timeless look, analog film quality',
    negative_prompt: 'modern digital, harsh lighting, low quality, blurry, contemporary, plastic look',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Vintage film aesthetic with timeless appeal'
  },
  'creative-minimalist': {
    model: MODELS.CREATIVE,
    prompt: 'minimalist portrait, clean simple background, modern aesthetic, professional simplicity, contemporary design',
    negative_prompt: 'cluttered, busy background, low quality, blurry, complex, messy, distracting elements',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Clean minimalist design approach'
  },

  // Actor & Model styles
  'actor-commercial': {
    model: MODELS.CREATIVE,
    prompt: 'commercial headshot, bright even lighting, friendly approachable expression, casting-ready photo, professional actor headshot',
    negative_prompt: 'dramatic, moody, poor lighting, low quality, blurry, unprofessional, amateur',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Commercial casting-ready headshot'
  },
  'actor-dramatic': {
    model: MODELS.CREATIVE,
    prompt: 'dramatic actor headshot, moody lighting, intense expression, theatrical quality, character depth, cinematic feel',
    negative_prompt: 'commercial, bright lighting, casual, low quality, blurry, amateur, flat',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Dramatic theatrical headshot'
  },
  'model-fashion': {
    model: MODELS.CREATIVE,
    prompt: 'fashion model portrait, high-end lighting, striking pose, editorial quality, fashion photography style, model portfolio',
    negative_prompt: 'amateur, poor lighting, low quality, blurry, casual, unprofessional, snapshot',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'High-end fashion model portfolio'
  },
  'model-commercial': {
    model: MODELS.CREATIVE,
    prompt: 'commercial model headshot, clean professional lighting, versatile look, advertising ready, mainstream appeal',
    negative_prompt: 'artistic, dramatic, poor lighting, low quality, blurry, niche, unprofessional',
    config: { guidance_scale: 5, ip_adapter_scale: 0.8, num_inference_steps: 30, controlnet_conditioning_scale: 0.8 },
    cost: 0.059,
    description: 'Commercial modeling and advertising'
  }
}

export const STYLE_CATEGORIES = {
  professional: {
    name: 'Professional',
    description: 'Perfect for LinkedIn, resumes, and corporate profiles',
    styles: ['professional-corporate', 'professional-executive', 'professional-modern', 'professional-outdoor', 'professional-library'],
    icon: '💼'
  },
  casual: {
    name: 'Casual & Lifestyle',
    description: 'Natural, approachable photos for personal branding',
    styles: ['casual-outdoor', 'casual-coffee', 'casual-beach', 'casual-park'],
    icon: '🌿'
  },
  dating: {
    name: 'Dating & Social',
    description: 'Attractive photos for dating apps and social media',
    styles: ['dating-romantic', 'dating-fun', 'dating-elegant', 'dating-adventure'],
    icon: '💖'
  },
  creative: {
    name: 'Creative & Branding',
    description: 'Artistic shots for creatives and entrepreneurs',
    styles: ['creative-artistic', 'creative-urban', 'creative-studio', 'creative-vintage', 'creative-minimalist'],
    icon: '🎨'
  },
  actor: {
    name: 'Actor & Model',
    description: 'Professional headshots for casting and portfolios',
    styles: ['actor-commercial', 'actor-dramatic', 'model-fashion', 'model-commercial'],
    icon: '🎭'
  }
}

// Helper function to get style configuration
export const getStyleConfig = (styleId) => {
  return STYLE_CONFIGS[styleId] || null
}

// Helper function to determine gender from image (placeholder - would use actual AI analysis)
export const detectGender = (imageUrl) => {
  // This would be replaced with actual gender detection
  // For now, return 'auto' to let the model decide
  return 'auto'
}

// Helper function to prepare API call based on style
export const prepareApiCall = (styleId, imageUrl, options = {}) => {
  const config = getStyleConfig(styleId)
  if (!config) {
    throw new Error(`Unknown style: ${styleId}`)
  }

  if (config.model === MODELS.PROFESSIONAL) {
    return {
      model: config.model,
      input: {
        input_image: imageUrl,
        gender: options.gender || detectGender(imageUrl),
        background: config.config.background,
        aspect_ratio: config.config.aspect_ratio
      }
    }
  } else if (config.model === MODELS.CREATIVE) {
    return {
      model: 'zsxkib/instant-id:2e4785a4d80dadf580077b2244c8d7c05d8e3faac04a04c02d8e099dd2876789',
      input: {
        image: imageUrl,
        prompt: config.prompt,
        negative_prompt: config.negative_prompt,
        scheduler: 'EulerDiscreteScheduler',
        enable_lcm: false,
        num_outputs: 1,
        sdxl_weights: 'protovision-xl-high-fidel',
        output_format: 'webp',
        pose_strength: 0.4,
        canny_strength: 0.3,
        depth_strength: 0.5,
        guidance_scale: config.config.guidance_scale,
        output_quality: 80,
        ip_adapter_scale: config.config.ip_adapter_scale,
        lcm_guidance_scale: 1.5,
        num_inference_steps: config.config.num_inference_steps,
        enable_pose_controlnet: true,
        enhance_nonface_region: true,
        enable_canny_controlnet: false,
        enable_depth_controlnet: false,
        lcm_num_inference_steps: 5,
        face_detection_input_width: 640,
        face_detection_input_height: 640,
        controlnet_conditioning_scale: config.config.controlnet_conditioning_scale
      }
    }
  }

  throw new Error(`Unsupported model: ${config.model}`)
}

// Cost calculation helper
export const calculateCost = (styleId) => {
  const config = getStyleConfig(styleId)
  return config ? config.cost : 0
}
