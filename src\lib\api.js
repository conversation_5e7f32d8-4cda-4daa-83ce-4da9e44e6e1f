// API client for HeadGenius Pro backend

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api'

class ApiClient {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error)
      throw error
    }
  }

  // Health check
  async healthCheck() {
    return this.request('/health')
  }

  // Image upload
  async uploadImage(file, userId) {
    const formData = new FormData()
    formData.append('image', file)
    formData.append('userId', userId)

    return this.request('/upload', {
      method: 'POST',
      headers: {}, // Let browser set Content-Type for FormData
      body: formData,
    })
  }

  // Generate headshot
  async generateHeadshot(styleId, imageUrl, userId, gender = 'auto') {
    return this.request('/generate', {
      method: 'POST',
      body: JSON.stringify({
        styleId,
        imageUrl,
        userId,
        gender,
      }),
    })
  }

  // Check generation status
  async getGenerationStatus(predictionId) {
    return this.request(`/generate/${predictionId}/status`)
  }

  // Get user credits
  async getUserCredits(userId) {
    return this.request(`/user/${userId}/credits`)
  }

  // Create Stripe checkout session
  async createCheckoutSession(priceId, userId) {
    return this.request('/payments/create-checkout-session', {
      method: 'POST',
      body: JSON.stringify({
        priceId,
        userId,
      }),
    })
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Convenience functions
export const uploadImage = (file, userId) => apiClient.uploadImage(file, userId)
export const generateHeadshot = (styleId, imageUrl, userId, gender) => 
  apiClient.generateHeadshot(styleId, imageUrl, userId, gender)
export const getGenerationStatus = (predictionId) => 
  apiClient.getGenerationStatus(predictionId)
export const getUserCredits = (userId) => apiClient.getUserCredits(userId)
export const createCheckoutSession = (priceId, userId) => 
  apiClient.createCheckoutSession(priceId, userId)

// Error types
export class ApiError extends Error {
  constructor(message, status, details) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.details = details
  }
}

// Utility functions
export const isValidImageFile = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const maxSize = 10 * 1024 * 1024 // 10MB
  
  return validTypes.includes(file.type) && file.size <= maxSize
}

export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
