import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Calendar, Clock, User, ArrowRight, Camera, Lightbulb, Zap, TrendingUp } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const heroRef = useRef(null)
  const articlesRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.blog-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.blog-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating particles
      gsap.to('.floating-particle', {
        y: -30,
        x: 15,
        rotation: 360,
        duration: 4,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.5
      })

      // Article cards animation
      gsap.fromTo('.article-card', 
        { y: 80, opacity: 0, scale: 0.9 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: articlesRef.current,
            start: 'top 80%'
          }
        }
      )
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const categories = [
    { id: 'all', name: 'All Posts', icon: '📚' },
    { id: 'tips', name: 'Photography Tips', icon: '📸' },
    { id: 'ai', name: 'AI Technology', icon: '🤖' },
    { id: 'business', name: 'Business', icon: '💼' },
    { id: 'trends', name: 'Industry Trends', icon: '📈' }
  ]

  const articles = [
    {
      id: 1,
      title: '10 Essential Tips for Perfect Professional Headshots',
      excerpt: 'Master the art of professional headshots with these expert tips that will make you stand out from the crowd.',
      category: 'tips',
      author: 'HeadGenius Team',
      date: '2025-01-15',
      readTime: '5 min read',
      image: '/api/placeholder/400/250',
      featured: true
    },
    {
      id: 2,
      title: 'How AI is Revolutionizing Professional Photography',
      excerpt: 'Explore the cutting-edge technology behind AI-generated headshots and what it means for the future of photography.',
      category: 'ai',
      author: 'Tech Insights',
      date: '2025-01-12',
      readTime: '7 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 3,
      title: 'LinkedIn Profile Photos: What Works in 2025',
      excerpt: 'Discover the latest trends in LinkedIn profile photos and how to make a lasting first impression.',
      category: 'business',
      author: 'Career Expert',
      date: '2025-01-10',
      readTime: '4 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 4,
      title: 'The Psychology of First Impressions in Digital Age',
      excerpt: 'Understanding how profile photos influence perception and decision-making in professional contexts.',
      category: 'business',
      author: 'Dr. Sarah Johnson',
      date: '2025-01-08',
      readTime: '6 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 5,
      title: 'Lighting Secrets: Natural vs Studio vs AI',
      excerpt: 'Compare different lighting approaches and learn why AI-generated lighting often surpasses traditional methods.',
      category: 'tips',
      author: 'Photography Pro',
      date: '2025-01-05',
      readTime: '8 min read',
      image: '/api/placeholder/400/250'
    },
    {
      id: 6,
      title: 'Future of Professional Photography: 2025 Predictions',
      excerpt: 'Industry experts share their predictions for how AI will continue to transform professional photography.',
      category: 'trends',
      author: 'Industry Analyst',
      date: '2025-01-03',
      readTime: '5 min read',
      image: '/api/placeholder/400/250'
    }
  ]

  const filteredArticles = selectedCategory === 'all' 
    ? articles 
    : articles.filter(article => article.category === selectedCategory)

  const featuredArticle = articles.find(article => article.featured)

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          {/* Floating particles */}
          <div className="floating-particle absolute top-10 left-10 w-4 h-4 bg-primary-400 rounded-full opacity-30"></div>
          <div className="floating-particle absolute top-20 right-20 w-6 h-6 bg-accent-400 rounded-full opacity-20"></div>
          <div className="floating-particle absolute bottom-20 left-20 w-5 h-5 bg-primary-500 rounded-full opacity-25"></div>
          <div className="floating-particle absolute bottom-10 right-10 w-3 h-3 bg-accent-500 rounded-full opacity-35"></div>
          
          <h1 className="blog-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            HeadGenius <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Blog</span>
          </h1>
          <p className="blog-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Insights, tips, and trends in AI-powered professional photography
          </p>
        </div>
      </section>

      {/* Featured Article */}
      {featuredArticle && (
        <section className="py-16">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200 overflow-hidden"
            >
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <span className="bg-gradient-to-r from-primary-600 to-accent-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Featured
                    </span>
                    <span className="text-secondary-500 text-sm">{featuredArticle.category}</span>
                  </div>
                  <h2 className="text-3xl font-bold text-secondary-900 mb-4">{featuredArticle.title}</h2>
                  <p className="text-secondary-600 mb-6 leading-relaxed">{featuredArticle.excerpt}</p>
                  <div className="flex items-center gap-4 text-sm text-secondary-500 mb-6">
                    <div className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {featuredArticle.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(featuredArticle.date).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {featuredArticle.readTime}
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="btn-primary inline-flex items-center gap-2"
                  >
                    Read Article
                    <ArrowRight className="w-4 h-4" />
                  </motion.button>
                </div>
                <div className="relative">
                  <div className="aspect-video bg-secondary-100 rounded-lg flex items-center justify-center">
                    <Camera className="w-16 h-16 text-secondary-400" />
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      )}

      {/* Category Filter */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-full border transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white border-primary-600'
                    : 'bg-white border-secondary-200 hover:border-primary-300 hover:bg-primary-50 text-secondary-700'
                }`}
              >
                <span>{category.icon}</span>
                <span className="font-medium">{category.name}</span>
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-16" ref={articlesRef}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredArticles.filter(article => !article.featured).map((article, index) => (
              <motion.article
                key={article.id}
                className="article-card card group hover:shadow-xl transition-all duration-300 cursor-pointer"
                whileHover={{ y: -5 }}
              >
                <div className="aspect-video bg-secondary-100 rounded-lg mb-4 overflow-hidden">
                  <div className="w-full h-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Camera className="w-12 h-12 text-secondary-400" />
                  </div>
                </div>
                
                <div className="flex items-center gap-2 mb-3">
                  <span className="bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs font-medium">
                    {categories.find(cat => cat.id === article.category)?.name}
                  </span>
                </div>
                
                <h3 className="text-xl font-bold text-secondary-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                  {article.title}
                </h3>
                
                <p className="text-secondary-600 mb-4 leading-relaxed">{article.excerpt}</p>
                
                <div className="flex items-center justify-between text-sm text-secondary-500">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {article.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {article.readTime}
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(article.date).toLocaleDateString()}
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="py-20 bg-secondary-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold mb-4">Stay Updated</h2>
            <p className="text-xl text-secondary-300 mb-8 max-w-2xl mx-auto">
              Get the latest insights on AI photography, professional tips, and industry trends delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg text-secondary-900 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-primary-600 hover:bg-primary-700 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200"
              >
                Subscribe
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Blog
