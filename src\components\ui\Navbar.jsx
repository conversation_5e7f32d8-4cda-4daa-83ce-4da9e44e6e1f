import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Camera, User, LogOut, CreditCard } from 'lucide-react'
import { useAuthStore } from '../../store/authStore'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()
  const { user, credits, signOut } = useAuthStore()

  const handleSignOut = async () => {
    await signOut()
    setIsOpen(false)
  }

  const isActive = (path) => location.pathname === path

  return (
    <nav className="bg-white/80 backdrop-blur-md border-b border-secondary-200 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 font-bold text-xl text-secondary-900">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-accent-600 rounded-lg flex items-center justify-center">
              <Camera className="w-5 h-5 text-white" />
            </div>
            HeadGenius Pro
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-8">
            <Link
              to="/"
              className={`font-medium transition-colors duration-200 ${
                isActive('/') ? 'text-primary-600' : 'text-secondary-600 hover:text-secondary-900'
              }`}
            >
              Home
            </Link>
            <Link
              to="/generate"
              className={`font-medium transition-colors duration-200 ${
                isActive('/generate') ? 'text-primary-600' : 'text-secondary-600 hover:text-secondary-900'
              }`}
            >
              Generate
            </Link>
            <Link
              to="/gallery"
              className={`font-medium transition-colors duration-200 ${
                isActive('/gallery') ? 'text-primary-600' : 'text-secondary-600 hover:text-secondary-900'
              }`}
            >
              Gallery
            </Link>
            <Link
              to="/pricing"
              className={`font-medium transition-colors duration-200 ${
                isActive('/pricing') ? 'text-primary-600' : 'text-secondary-600 hover:text-secondary-900'
              }`}
            >
              Pricing
            </Link>

            {user ? (
              <div className="flex items-center gap-4">
                {/* Credits Display */}
                <div className="flex items-center gap-2 bg-secondary-100 px-3 py-1 rounded-full">
                  <CreditCard className="w-4 h-4 text-secondary-600" />
                  <span className="text-sm font-medium text-secondary-700">
                    {credits.remaining} credits
                  </span>
                </div>

                {/* User Menu */}
                <div className="relative group">
                  <button className="flex items-center gap-2 p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200">
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                  </button>
                  
                  <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="p-3 border-b border-secondary-200">
                      <p className="text-sm font-medium text-secondary-900">{user.email}</p>
                      <p className="text-xs text-secondary-500">
                        {credits.tier === 'free' ? 'Free Tier' : `${credits.tier} Plan`}
                      </p>
                    </div>
                    <div className="p-1">
                      <Link
                        to="/dashboard"
                        className="flex items-center gap-2 w-full p-2 text-left text-sm text-secondary-700 hover:bg-secondary-100 rounded transition-colors duration-200"
                      >
                        <User className="w-4 h-4" />
                        Dashboard
                      </Link>
                      <button
                        onClick={handleSignOut}
                        className="flex items-center gap-2 w-full p-2 text-left text-sm text-red-600 hover:bg-red-50 rounded transition-colors duration-200"
                      >
                        <LogOut className="w-4 h-4" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-4">
                <Link
                  to="/auth"
                  className="text-secondary-600 hover:text-secondary-900 font-medium transition-colors duration-200"
                >
                  Sign In
                </Link>
                <Link
                  to="/auth"
                  className="btn-primary"
                >
                  Get Started
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200"
          >
            {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-secondary-200">
            <div className="flex flex-col gap-4">
              <Link
                to="/"
                onClick={() => setIsOpen(false)}
                className={`font-medium transition-colors duration-200 ${
                  isActive('/') ? 'text-primary-600' : 'text-secondary-600'
                }`}
              >
                Home
              </Link>
              <Link
                to="/generate"
                onClick={() => setIsOpen(false)}
                className={`font-medium transition-colors duration-200 ${
                  isActive('/generate') ? 'text-primary-600' : 'text-secondary-600'
                }`}
              >
                Generate
              </Link>
              <Link
                to="/gallery"
                onClick={() => setIsOpen(false)}
                className={`font-medium transition-colors duration-200 ${
                  isActive('/gallery') ? 'text-primary-600' : 'text-secondary-600'
                }`}
              >
                Gallery
              </Link>
              <Link
                to="/pricing"
                onClick={() => setIsOpen(false)}
                className={`font-medium transition-colors duration-200 ${
                  isActive('/pricing') ? 'text-primary-600' : 'text-secondary-600'
                }`}
              >
                Pricing
              </Link>

              {user ? (
                <div className="pt-4 border-t border-secondary-200">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-secondary-900">{user.email}</p>
                      <p className="text-xs text-secondary-500">{credits.remaining} credits</p>
                    </div>
                  </div>
                  <Link
                    to="/dashboard"
                    onClick={() => setIsOpen(false)}
                    className="block py-2 text-secondary-600 font-medium"
                  >
                    Dashboard
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="block py-2 text-red-600 font-medium"
                  >
                    Sign Out
                  </button>
                </div>
              ) : (
                <div className="pt-4 border-t border-secondary-200 flex flex-col gap-4">
                  <Link
                    to="/auth"
                    onClick={() => setIsOpen(false)}
                    className="text-secondary-600 font-medium"
                  >
                    Sign In
                  </Link>
                  <Link
                    to="/auth"
                    onClick={() => setIsOpen(false)}
                    className="btn-primary text-center"
                  >
                    Get Started
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
