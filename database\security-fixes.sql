-- HeadGenius Pro: Security Fixes for Supabase Database
-- Run this in your Supabase SQL Editor to fix security warnings

-- =============================================
-- FIX 1: Enable RLS on style_analytics table (if it exists)
-- =============================================

-- Enable Row Level Security on style_analytics table
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'style_analytics') THEN
        ALTER TABLE style_analytics ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- =============================================
-- FIX 2: Add Contact Submissions Table
-- =============================================

-- Create contact_submissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS contact_submissions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    assigned_to TEXT,
    response TEXT,
    responded_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for contact_submissions
CREATE INDEX IF NOT EXISTS idx_contact_submissions_email ON contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at DESC);

-- Enable RLS on contact_submissions
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for contact_submissions
DROP POLICY IF EXISTS "Anyone can submit contact forms" ON contact_submissions;
DROP POLICY IF EXISTS "Users can view own submissions" ON contact_submissions;

CREATE POLICY "Anyone can submit contact forms" ON contact_submissions FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can view own submissions" ON contact_submissions FOR SELECT USING (
    (select auth.uid()) = user_id OR user_id IS NULL
);

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_contact_submissions_updated_at ON contact_submissions;
CREATE TRIGGER update_contact_submissions_updated_at
    BEFORE UPDATE ON contact_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- FIX 3: Fix function search_path security warnings
-- =============================================

-- Fix update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix handle_new_user function
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (user_id, full_name)
    VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');
    
    INSERT INTO user_credits (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix update_style_analytics function
CREATE OR REPLACE FUNCTION update_style_analytics(style_id_param TEXT)
RETURNS VOID AS $$
BEGIN
    INSERT INTO style_analytics (style_id, generation_count, last_used_at)
    VALUES (style_id_param, 1, NOW())
    ON CONFLICT (style_id)
    DO UPDATE SET
        generation_count = style_analytics.generation_count + 1,
        last_used_at = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix get_retention_period function
CREATE OR REPLACE FUNCTION get_retention_period(user_tier TEXT)
RETURNS INTERVAL AS $$
BEGIN
    CASE user_tier
        WHEN 'free' THEN RETURN INTERVAL '24 hours';
        WHEN 'starter' THEN RETURN INTERVAL '30 days';
        WHEN 'pro' THEN RETURN INTERVAL '90 days';
        ELSE RETURN INTERVAL '24 hours'; -- Default to free tier
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix cleanup_expired_generations function
CREATE OR REPLACE FUNCTION cleanup_expired_generations()
RETURNS INTEGER AS $$
DECLARE
    expired_generation RECORD;
    deleted_count INTEGER := 0;
    storage_path TEXT;
BEGIN
    -- Find expired generations based on user tier and retention period
    FOR expired_generation IN
        SELECT
            g.id,
            g.user_id,
            g.input_image_url,
            g.output_image_url,
            uc.subscription_tier,
            g.created_at
        FROM generations g
        JOIN user_credits uc ON g.user_id = uc.user_id
        WHERE g.created_at < (NOW() - get_retention_period(uc.subscription_tier))
        AND g.status = 'completed'
    LOOP
        -- Delete input image from storage if it exists
        IF expired_generation.input_image_url LIKE '%supabase%' THEN
            storage_path := regexp_replace(
                expired_generation.input_image_url,
                '.*\/storage\/v1\/object\/public\/[^\/]+\/',
                ''
            );
            
            PERFORM storage.delete_object('user-uploads', storage_path);
        END IF;
        
        -- Delete output image from storage if it exists
        IF expired_generation.output_image_url LIKE '%supabase%' THEN
            storage_path := regexp_replace(
                expired_generation.output_image_url,
                '.*\/storage\/v1\/object\/public\/[^\/]+\/',
                ''
            );
            
            PERFORM storage.delete_object('generated-headshots', storage_path);
        END IF;
        
        -- Delete generation record
        DELETE FROM generations WHERE id = expired_generation.id;
        deleted_count := deleted_count + 1;
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix cleanup_orphaned_files function
CREATE OR REPLACE FUNCTION cleanup_orphaned_files()
RETURNS INTEGER AS $$
DECLARE
    orphaned_file RECORD;
    deleted_count INTEGER := 0;
    file_user_id TEXT;
BEGIN
    -- Cleanup orphaned files in user-uploads bucket
    FOR orphaned_file IN
        SELECT name FROM storage.objects
        WHERE bucket_id = 'user-uploads'
        AND created_at < NOW() - INTERVAL '7 days'
    LOOP
        file_user_id := (storage.foldername(orphaned_file.name))[1];
        
        -- Check if file is referenced in generations table
        IF NOT EXISTS (
            SELECT 1 FROM generations 
            WHERE input_image_url LIKE '%' || orphaned_file.name || '%'
        ) THEN
            PERFORM storage.delete_object('user-uploads', orphaned_file.name);
            deleted_count := deleted_count + 1;
        END IF;
    END LOOP;
    
    -- Cleanup orphaned files in generated-headshots bucket
    FOR orphaned_file IN
        SELECT name FROM storage.objects
        WHERE bucket_id = 'generated-headshots'
        AND created_at < NOW() - INTERVAL '7 days'
    LOOP
        file_user_id := (storage.foldername(orphaned_file.name))[1];
        
        -- Check if file is referenced in generations table
        IF NOT EXISTS (
            SELECT 1 FROM generations 
            WHERE output_image_url LIKE '%' || orphaned_file.name || '%'
        ) THEN
            PERFORM storage.delete_object('generated-headshots', orphaned_file.name);
            deleted_count := deleted_count + 1;
        END IF;
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix get_user_storage_usage function
CREATE OR REPLACE FUNCTION get_user_storage_usage(user_uuid UUID)
RETURNS TABLE(
    total_files INTEGER,
    total_size_bytes BIGINT,
    input_files INTEGER,
    output_files INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_files,
        COALESCE(SUM(metadata->>'size')::BIGINT, 0) as total_size_bytes,
        COUNT(*) FILTER (WHERE bucket_id = 'user-uploads')::INTEGER as input_files,
        COUNT(*) FILTER (WHERE bucket_id = 'generated-headshots')::INTEGER as output_files
    FROM storage.objects
    WHERE user_uuid::text = (storage.foldername(name))[1];
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix generate_storage_path function
CREATE OR REPLACE FUNCTION generate_storage_path(user_uuid UUID, file_type TEXT, file_extension TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN user_uuid::text || '/' || file_type || '/' ||
           extract(epoch from now())::bigint || '_' ||
           substr(md5(random()::text), 1, 8) || '.' || file_extension;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Fix check_storage_quota function
CREATE OR REPLACE FUNCTION check_storage_quota(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_tier TEXT;
    current_usage BIGINT;
    quota_limit BIGINT;
BEGIN
    -- Get user tier
    SELECT subscription_tier INTO user_tier
    FROM user_credits
    WHERE user_id = user_uuid;

    -- Set quota limits (in bytes)
    CASE user_tier
        WHEN 'free' THEN quota_limit := 100 * 1024 * 1024; -- 100MB
        WHEN 'starter' THEN quota_limit := 500 * 1024 * 1024; -- 500MB
        WHEN 'pro' THEN quota_limit := 2 * 1024 * 1024 * 1024; -- 2GB
        ELSE quota_limit := 100 * 1024 * 1024; -- Default 100MB
    END CASE;

    -- Get current usage
    SELECT COALESCE(SUM(metadata->>'size')::BIGINT, 0) INTO current_usage
    FROM storage.objects
    WHERE user_uuid::text = (storage.foldername(name))[1];

    RETURN current_usage < quota_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- =============================================
-- FIX 3: Optimize RLS Policies for Performance
-- =============================================

-- Drop existing policies that have performance issues
DROP POLICY IF EXISTS "Users can view own credits" ON user_credits;
DROP POLICY IF EXISTS "Users can update own credits" ON user_credits;
DROP POLICY IF EXISTS "Users can insert own credits" ON user_credits;
DROP POLICY IF EXISTS "Users can view own generations" ON generations;
DROP POLICY IF EXISTS "Users can insert own generations" ON generations;
DROP POLICY IF EXISTS "Users can update own generations" ON generations;
DROP POLICY IF EXISTS "Users can view own payments" ON payments;
DROP POLICY IF EXISTS "Users can insert own payments" ON payments;
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON user_profiles;

-- Create optimized policies using (select auth.uid())
-- User Credits Policies
CREATE POLICY "Users can view own credits" ON user_credits FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own credits" ON user_credits FOR UPDATE USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own credits" ON user_credits FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Generations Policies
CREATE POLICY "Users can view own generations" ON generations FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own generations" ON generations FOR INSERT WITH CHECK ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own generations" ON generations FOR UPDATE USING ((select auth.uid()) = user_id);

-- Payments Policies
CREATE POLICY "Users can view own payments" ON payments FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own payments" ON payments FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- User Profiles Policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Check that RLS is enabled on style_analytics
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'style_analytics';

-- Verify all functions have proper search_path
SELECT 
    routine_name,
    routine_type,
    security_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'update_updated_at_column',
    'handle_new_user', 
    'update_style_analytics',
    'get_retention_period',
    'cleanup_expired_generations',
    'cleanup_orphaned_files',
    'get_user_storage_usage',
    'generate_storage_path',
    'check_storage_quota'
);
