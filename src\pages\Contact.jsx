import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Mail, Phone, MapPin, Clock, Send, MessageCircle, Headphones, Zap } from 'lucide-react'
import toast from 'react-hot-toast'
import { storage } from '../lib/supabase'
import { useAuthStore } from '../store/authStore'

gsap.registerPlugin(ScrollTrigger)

const Contact = () => {
  const { user } = useAuthStore()
  const [formData, setFormData] = useState({
    name: '',
    email: user?.email || '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const heroRef = useRef(null)
  const formRef = useRef(null)
  const infoRef = useRef(null)

  // Update email when user changes
  useEffect(() => {
    if (user?.email && !formData.email) {
      setFormData(prev => ({ ...prev, email: user.email }))
    }
  }, [user, formData.email])

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo('.contact-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.contact-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating icons animation
      gsap.to('.floating-contact-icon', {
        y: -20,
        rotation: 10,
        duration: 3,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.4
      })

      // Form animation
      gsap.fromTo('.form-container', 
        { x: -100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: formRef.current,
            start: 'top 80%'
          }
        }
      )

      // Info cards animation
      gsap.fromTo('.info-card', 
        { x: 100, opacity: 0, scale: 0.9 },
        {
          x: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'back.out(1.4)',
          scrollTrigger: {
            trigger: infoRef.current,
            start: 'top 80%'
          }
        }
      )
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.subject.trim() || !formData.message.trim()) {
      toast.error('Please fill in all fields')
      return
    }

    if (!formData.email.includes('@')) {
      toast.error('Please enter a valid email address')
      return
    }

    setIsSubmitting(true)

    try {
      const { data, error } = await storage.submitContactForm(formData, user?.id)

      if (error) {
        throw error
      }

      toast.success('Message sent successfully! We\'ll get back to you soon.')

      // Reset form but keep email if user is logged in
      setFormData({
        name: '',
        email: user?.email || '',
        subject: '',
        message: ''
      })
    } catch (error) {
      console.error('Contact form submission error:', error)
      toast.error('Failed to send message. Please try again or contact us directly.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Get help with your account or technical issues',
      contact: '<EMAIL>',
      action: 'Send Email'
    },
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      contact: 'Available 24/7',
      action: 'Start Chat'
    },
    {
      icon: Headphones,
      title: 'Priority Support',
      description: 'Dedicated support for Pro subscribers',
      contact: 'Pro members only',
      action: 'Contact Pro Support'
    }
  ]

  const businessInfo = [
    {
      icon: MapPin,
      title: 'Headquarters',
      info: 'San Francisco, CA\nUnited States'
    },
    {
      icon: Clock,
      title: 'Support Hours',
      info: 'Monday - Friday: 9AM - 6PM PST\nWeekends: 10AM - 4PM PST'
    },
    {
      icon: Zap,
      title: 'Response Time',
      info: 'Average response: 2 hours\nPro support: 30 minutes'
    }
  ]

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          {/* Floating icons */}
          <div className="floating-contact-icon absolute top-10 left-10 text-primary-400 opacity-20">
            <Mail className="w-16 h-16" />
          </div>
          <div className="floating-contact-icon absolute top-20 right-20 text-accent-400 opacity-20">
            <MessageCircle className="w-12 h-12" />
          </div>
          <div className="floating-contact-icon absolute bottom-20 left-20 text-primary-400 opacity-20">
            <Headphones className="w-14 h-14" />
          </div>
          
          <h1 className="contact-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            Get in <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">Touch</span>
          </h1>
          <p className="contact-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            Have questions? Need support? We're here to help you create amazing headshots.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-16" ref={infoRef}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <motion.div
                key={index}
                className="info-card card text-center group hover:shadow-xl transition-all duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <info.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-secondary-900 mb-3">{info.title}</h3>
                <p className="text-secondary-600 mb-4">{info.description}</p>
                <p className="text-primary-600 font-medium mb-4">{info.contact}</p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-secondary text-sm"
                >
                  {info.action}
                </motion.button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Business Info */}
      <section className="py-16" ref={formRef}>
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="form-container">
              <div className="card">
                <h2 className="text-3xl font-bold text-secondary-900 mb-6">Send us a Message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-2">
                        Your Name
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                        placeholder="John Doe"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-secondary-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Subject
                    </label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                      placeholder="How can we help you?"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-secondary-700 mb-2">
                      Message
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 resize-none"
                      placeholder="Tell us more about your question or issue..."
                    />
                  </div>
                  
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full btn-primary inline-flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Send Message
                      </>
                    )}
                  </motion.button>
                </form>
              </div>
            </div>

            {/* Business Information */}
            <div className="space-y-8">
              <div className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200">
                <h3 className="text-2xl font-bold text-secondary-900 mb-6">Business Information</h3>
                <div className="space-y-6">
                  {businessInfo.map((item, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-accent-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <item.icon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-secondary-900 mb-1">{item.title}</h4>
                        <p className="text-secondary-600 whitespace-pre-line">{item.info}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="card">
                <h3 className="text-xl font-bold text-secondary-900 mb-4">Frequently Asked Questions</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">How quickly will I receive a response?</h4>
                    <p className="text-secondary-600 text-sm">We typically respond within 2 hours during business hours. Pro subscribers receive priority support with 30-minute response times.</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Do you offer phone support?</h4>
                    <p className="text-secondary-600 text-sm">Currently, we provide support through email and live chat. Phone support is available for enterprise customers.</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Can I schedule a demo?</h4>
                    <p className="text-secondary-600 text-sm">Yes! Contact us to schedule a personalized demo of HeadGenius Pro's features and capabilities.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Contact
