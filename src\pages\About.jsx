import { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Camera, Sparkles, Users, Target, Heart, Zap, Award, Globe } from 'lucide-react'

gsap.registerPlugin(ScrollTrigger)

const About = () => {
  const heroRef = useRef(null)
  const statsRef = useRef(null)
  const missionRef = useRef(null)
  const valuesRef = useRef(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero section animations
      gsap.fromTo('.hero-title', 
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: 'power3.out' }
      )
      
      gsap.fromTo('.hero-subtitle', 
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, delay: 0.3, ease: 'power3.out' }
      )

      // Floating animation for icons
      gsap.to('.floating-icon', {
        y: -20,
        duration: 2,
        ease: 'power2.inOut',
        yoyo: true,
        repeat: -1,
        stagger: 0.3
      })

      // Stats counter animation
      gsap.fromTo('.stat-number', 
        { textContent: 0 },
        {
          textContent: (i, target) => target.getAttribute('data-value'),
          duration: 2,
          ease: 'power2.out',
          snap: { textContent: 1 },
          scrollTrigger: {
            trigger: statsRef.current,
            start: 'top 80%'
          }
        }
      )

      // Mission cards stagger animation
      gsap.fromTo('.mission-card', 
        { y: 100, opacity: 0, scale: 0.8 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: missionRef.current,
            start: 'top 80%'
          }
        }
      )

      // Values section wave animation
      gsap.fromTo('.value-item', 
        { x: -100, opacity: 0 },
        {
          x: 0,
          opacity: 1,
          duration: 1,
          stagger: 0.3,
          ease: 'power3.out',
          scrollTrigger: {
            trigger: valuesRef.current,
            start: 'top 80%'
          }
        }
      )
    }, heroRef)

    return () => ctx.revert()
  }, [])

  const stats = [
    { number: 50000, label: 'Headshots Generated', icon: Camera },
    { number: 15000, label: 'Happy Customers', icon: Users },
    { number: 98, label: 'Satisfaction Rate', icon: Heart, suffix: '%' },
    { number: 24, label: 'Countries Served', icon: Globe }
  ]

  const missions = [
    {
      icon: Target,
      title: 'Our Mission',
      description: 'To democratize professional photography by making high-quality headshots accessible to everyone, anywhere in the world.'
    },
    {
      icon: Sparkles,
      title: 'Our Vision',
      description: 'To become the global leader in AI-powered professional photography, setting new standards for quality and user experience.'
    },
    {
      icon: Zap,
      title: 'Our Innovation',
      description: 'Combining cutting-edge AI technology with intuitive design to create the most advanced headshot generation platform.'
    }
  ]

  const values = [
    { icon: Award, title: 'Excellence', description: 'We strive for perfection in every headshot we generate' },
    { icon: Users, title: 'Accessibility', description: 'Professional headshots should be available to everyone' },
    { icon: Heart, title: 'Empathy', description: 'We understand the importance of making a great first impression' },
    { icon: Zap, title: 'Innovation', description: 'Constantly pushing the boundaries of what AI can achieve' }
  ]

  return (
    <div className="min-h-screen gradient-bg" ref={heroRef}>
      {/* Hero Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="floating-icon absolute top-10 left-10 text-primary-400 opacity-20">
            <Camera className="w-16 h-16" />
          </div>
          <div className="floating-icon absolute top-20 right-20 text-accent-400 opacity-20">
            <Sparkles className="w-12 h-12" />
          </div>
          <div className="floating-icon absolute bottom-20 left-20 text-primary-400 opacity-20">
            <Users className="w-14 h-14" />
          </div>
          
          <h1 className="hero-title text-5xl md:text-7xl font-bold text-secondary-900 mb-6">
            About <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">HeadGenius</span>
          </h1>
          <p className="hero-subtitle text-xl md:text-2xl text-secondary-600 max-w-3xl mx-auto leading-relaxed">
            We're revolutionizing professional photography with AI technology that creates stunning headshots in minutes, not hours.
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm" ref={statsRef}>
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-600 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-bold text-secondary-900 mb-2">
                  <span className="stat-number" data-value={stat.number}>0</span>
                  {stat.suffix && <span>{stat.suffix}</span>}
                </div>
                <p className="text-secondary-600 font-medium">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20" ref={missionRef}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-secondary-900 mb-4">
              Why We Built HeadGenius
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Born from the frustration of expensive, time-consuming traditional photography sessions
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {missions.map((mission, index) => (
              <div key={index} className="mission-card card text-center group hover:shadow-xl transition-all duration-300">
                <div className="w-20 h-20 bg-gradient-to-br from-primary-600 to-accent-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <mission.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-secondary-900 mb-4">{mission.title}</h3>
                <p className="text-secondary-600 leading-relaxed">{mission.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-secondary-900 text-white" ref={valuesRef}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Our Values</h2>
            <p className="text-xl text-secondary-300 max-w-2xl mx-auto">
              The principles that guide everything we do at HeadGenius
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className="value-item flex items-start gap-6 p-6 rounded-lg bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-accent-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <value.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">{value.title}</h3>
                  <p className="text-secondary-300">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="card bg-gradient-to-r from-primary-50 to-accent-50 border-primary-200"
            >
              <h2 className="text-3xl font-bold text-secondary-900 mb-6">Our Story</h2>
              <div className="prose prose-lg text-secondary-700">
                <p className="mb-4">
                  HeadGenius was born from a simple frustration: getting professional headshots was too expensive, 
                  time-consuming, and often disappointing. As a solo founder, I experienced firsthand the struggle 
                  of needing quality professional photos for LinkedIn, websites, and business materials.
                </p>
                <p className="mb-4">
                  Traditional photography sessions cost hundreds of dollars, required scheduling weeks in advance, 
                  and often resulted in photos that didn't quite capture what you were looking for. There had to 
                  be a better way.
                </p>
                <p>
                  That's when I discovered the potential of AI-powered image generation. After months of research, 
                  development, and countless iterations, HeadGenius Pro was born - a platform that delivers 
                  professional-quality headshots in minutes, not weeks, at a fraction of the traditional cost.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold mb-4">Ready to Join Our Mission?</h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
              Experience the future of professional photography. Create your perfect headshot today.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-primary-600 font-bold py-4 px-8 rounded-lg text-lg hover:bg-secondary-50 transition-colors duration-200 inline-flex items-center gap-2"
            >
              <Camera className="w-5 h-5" />
              Start Creating
            </motion.button>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default About
