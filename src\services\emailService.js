// Email Service for HeadGenius Pro
// Handles transactional emails using Zoho Mail SMTP

import nodemailer from 'nodemailer'

class EmailService {
  constructor() {
    this.transporter = null
    this.initialized = false
  }

  // Initialize email transporter
  async initialize() {
    if (this.initialized) return

    try {
      this.transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT),
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      })

      // Verify connection
      await this.transporter.verify()
      this.initialized = true
      console.log('Email service initialized successfully')
    } catch (error) {
      console.error('Email service initialization failed:', error)
      throw error
    }
  }

  // Send welcome email to new users
  async sendWelcomeEmail(userEmail, userName) {
    await this.initialize()

    const mailOptions = {
      from: `"HeadGenius Pro" <${process.env.SMTP_USER}>`,
      to: userEmail,
      subject: 'Welcome to HeadGenius Pro! 🎉',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to HeadGenius Pro!</h1>
          </div>
          
          <div style="padding: 40px 20px; background: #f8f9fa;">
            <h2 style="color: #333; margin-bottom: 20px;">Hi ${userName || 'there'}! 👋</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              Thank you for joining HeadGenius Pro! You're now ready to create stunning AI-generated headshots 
              that will make you stand out on LinkedIn, dating apps, and professional profiles.
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">🎁 Your Free Credits</h3>
              <p style="color: #666; margin-bottom: 0;">
                You get <strong>5 free headshots every 15 days</strong> to get started. 
                No credit card required!
              </p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.VITE_APP_URL}/generate" 
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                Generate Your First Headshot
              </a>
            </div>
            
            <h3 style="color: #333;">🚀 What's Next?</h3>
            <ul style="color: #666; line-height: 1.8;">
              <li>Upload a clear photo of yourself</li>
              <li>Choose from 22+ professional styles</li>
              <li>Download your high-resolution headshots</li>
              <li>Use them anywhere you need to look professional</li>
            </ul>
            
            <p style="color: #666; margin-top: 30px;">
              Need help? Just reply to this email or contact us at 
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            
            <p style="color: #666;">
              Best regards,<br>
              The HeadGenius Pro Team
            </p>
          </div>
        </div>
      `
    }

    return await this.transporter.sendMail(mailOptions)
  }

  // Send payment confirmation email
  async sendPaymentConfirmation(userEmail, planName, amount, creditsAdded) {
    await this.initialize()

    const mailOptions = {
      from: `"HeadGenius Pro" <${process.env.SMTP_USER}>`,
      to: userEmail,
      subject: 'Payment Confirmed - Credits Added! 💳',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #10b981; padding: 40px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Payment Successful! ✅</h1>
          </div>
          
          <div style="padding: 40px 20px; background: #f8f9fa;">
            <h2 style="color: #333;">Thank you for your purchase!</h2>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">📋 Order Details</h3>
              <p style="margin: 10px 0;"><strong>Plan:</strong> ${planName}</p>
              <p style="margin: 10px 0;"><strong>Amount:</strong> $${amount}</p>
              <p style="margin: 10px 0;"><strong>Credits Added:</strong> ${creditsAdded}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.VITE_APP_URL}/generate" 
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                Start Generating Headshots
              </a>
            </div>
            
            <p style="color: #666;">
              Your credits have been added to your account and you can start generating 
              professional headshots immediately.
            </p>
            
            <p style="color: #666; margin-top: 30px;">
              Questions? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
          </div>
        </div>
      `
    }

    return await this.transporter.sendMail(mailOptions)
  }

  // Send generation completion notification
  async sendGenerationComplete(userEmail, styleUsed, imageUrl) {
    await this.initialize()

    const mailOptions = {
      from: `"HeadGenius Pro" <${process.env.SMTP_USER}>`,
      to: userEmail,
      subject: 'Your Headshot is Ready! 📸',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Your Headshot is Ready! 🎉</h1>
          </div>
          
          <div style="padding: 40px 20px; background: #f8f9fa;">
            <p style="color: #666; line-height: 1.6;">
              Great news! Your <strong>${styleUsed}</strong> headshot has been generated and is ready for download.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.VITE_APP_URL}/dashboard" 
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                View Your Headshot
              </a>
            </div>
            
            <p style="color: #666;">
              You can download your high-resolution headshot and use it for LinkedIn, 
              dating profiles, professional websites, and more.
            </p>
          </div>
        </div>
      `
    }

    return await this.transporter.sendMail(mailOptions)
  }

  // Send support ticket response
  async sendSupportResponse(userEmail, ticketId, response) {
    await this.initialize()

    const mailOptions = {
      from: `"HeadGenius Support" <<EMAIL>>`,
      to: userEmail,
      subject: `Re: Support Ticket #${ticketId}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #667eea; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 24px;">HeadGenius Support</h1>
          </div>
          
          <div style="padding: 30px 20px; background: #f8f9fa;">
            <p style="color: #666;">
              Hi there! We've responded to your support ticket #${ticketId}.
            </p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
              ${response}
            </div>
            
            <p style="color: #666;">
              If you need further assistance, just reply to this email.
            </p>
            
            <p style="color: #666;">
              Best regards,<br>
              HeadGenius Support Team
            </p>
          </div>
        </div>
      `
    }

    return await this.transporter.sendMail(mailOptions)
  }

  // Send password reset email
  async sendPasswordReset(userEmail, resetLink) {
    await this.initialize()

    const mailOptions = {
      from: `"HeadGenius Pro" <${process.env.SMTP_USER}>`,
      to: userEmail,
      subject: 'Reset Your Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #667eea; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Reset Your Password</h1>
          </div>
          
          <div style="padding: 30px 20px; background: #f8f9fa;">
            <p style="color: #666;">
              You requested to reset your password. Click the button below to create a new password:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" 
                 style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                Reset Password
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              This link will expire in 1 hour. If you didn't request this, please ignore this email.
            </p>
          </div>
        </div>
      `
    }

    return await this.transporter.sendMail(mailOptions)
  }
}

// Create singleton instance
export const emailService = new EmailService()

// Export individual functions for convenience
export const sendWelcomeEmail = (email, name) => emailService.sendWelcomeEmail(email, name)
export const sendPaymentConfirmation = (email, plan, amount, credits) => 
  emailService.sendPaymentConfirmation(email, plan, amount, credits)
export const sendGenerationComplete = (email, style, imageUrl) => 
  emailService.sendGenerationComplete(email, style, imageUrl)
export const sendSupportResponse = (email, ticketId, response) => 
  emailService.sendSupportResponse(email, ticketId, response)
export const sendPasswordReset = (email, resetLink) => 
  emailService.sendPasswordReset(email, resetLink)
