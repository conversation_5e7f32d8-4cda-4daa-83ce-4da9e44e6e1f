import Stripe from 'stripe'
import { supabase } from '../lib/supabase.js'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

// Create checkout session
export const createCheckoutSession = async (req, res) => {
  try {
    const { priceId, userId, planId, successUrl, cancelUrl } = req.body

    if (!priceId || !userId || !planId) {
      return res.status(400).json({ 
        error: 'Missing required fields: priceId, userId, planId' 
      })
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl || `${process.env.VITE_APP_URL}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl || `${process.env.VITE_APP_URL}/pricing`,
      client_reference_id: userId,
      metadata: {
        userId,
        planId,
      },
    })

    // Save payment record
    const { error: dbError } = await supabase
      .from('payments')
      .insert({
        user_id: userId,
        stripe_session_id: session.id,
        amount: session.amount_total,
        currency: session.currency,
        status: 'pending',
        tier_purchased: planId,
        metadata: { planId, priceId }
      })

    if (dbError) {
      console.error('Database error:', dbError)
      // Continue anyway, we can reconcile later
    }

    res.json({ sessionId: session.id })

  } catch (error) {
    console.error('Checkout session error:', error)
    res.status(500).json({ 
      error: 'Failed to create checkout session',
      details: error.message 
    })
  }
}

// Verify payment session
export const verifyPaymentSession = async (req, res) => {
  try {
    const { sessionId } = req.params

    const session = await stripe.checkout.sessions.retrieve(sessionId)
    
    if (session.payment_status === 'paid') {
      // Update payment record
      const { error: updateError } = await supabase
        .from('payments')
        .update({
          status: 'succeeded',
          stripe_payment_intent_id: session.payment_intent,
          updated_at: new Date().toISOString()
        })
        .eq('stripe_session_id', sessionId)

      if (updateError) {
        console.error('Payment update error:', updateError)
      }

      // Add credits to user account
      const planCredits = {
        starter: 40,
        pro: 100
      }

      const creditsToAdd = planCredits[session.metadata.planId] || 0
      
      if (creditsToAdd > 0) {
        // Get current credits
        const { data: currentCredits } = await supabase
          .from('user_credits')
          .select('*')
          .eq('user_id', session.client_reference_id)
          .single()

        if (currentCredits) {
          // Update credits
          await supabase
            .from('user_credits')
            .update({
              total_credits: currentCredits.total_credits + creditsToAdd,
              subscription_tier: session.metadata.planId,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', session.client_reference_id)
        }
      }

      res.json({
        success: true,
        session: {
          id: session.id,
          status: session.payment_status,
          amount: session.amount_total,
          currency: session.currency,
          creditsAdded: creditsToAdd
        }
      })
    } else {
      res.json({
        success: false,
        session: {
          id: session.id,
          status: session.payment_status
        }
      })
    }

  } catch (error) {
    console.error('Payment verification error:', error)
    res.status(500).json({ 
      error: 'Failed to verify payment',
      details: error.message 
    })
  }
}

// Get payment history
export const getPaymentHistory = async (req, res) => {
  try {
    const { userId } = req.params

    const { data: payments, error } = await supabase
      .from('payments')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      throw error
    }

    res.json({
      success: true,
      payments: payments || []
    })

  } catch (error) {
    console.error('Payment history error:', error)
    res.status(500).json({ 
      error: 'Failed to fetch payment history',
      details: error.message 
    })
  }
}

// Stripe webhook handler
export const handleStripeWebhook = async (req, res) => {
  const sig = req.headers['stripe-signature']
  let event

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET)
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message)
    return res.status(400).send(`Webhook Error: ${err.message}`)
  }

  // Handle the event
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object
      console.log('Payment succeeded:', session.id)
      
      // Update payment status
      await supabase
        .from('payments')
        .update({
          status: 'succeeded',
          stripe_payment_intent_id: session.payment_intent,
          updated_at: new Date().toISOString()
        })
        .eq('stripe_session_id', session.id)
      
      break

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object
      console.log('Payment failed:', failedPayment.id)
      
      // Update payment status
      await supabase
        .from('payments')
        .update({
          status: 'failed',
          updated_at: new Date().toISOString()
        })
        .eq('stripe_payment_intent_id', failedPayment.id)
      
      break

    default:
      console.log(`Unhandled event type ${event.type}`)
  }

  res.json({ received: true })
}
