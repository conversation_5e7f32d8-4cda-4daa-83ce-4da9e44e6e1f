import { useState } from 'react'
import { motion } from 'framer-motion'
import { Upload, Camera, Sparkles, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react'
import { useAuthStore } from '../store/authStore'
import { useGenerationStore } from '../store/generationStore'
import { isValidImageFile, formatFileSize } from '../lib/api'
import { trackUpload, trackStyleSelection, trackGeneration } from '../lib/analytics'
import toast from 'react-hot-toast'

const Generate = () => {
  const [selectedStyle, setSelectedStyle] = useState(null)
  const [uploadedFile, setUploadedFile] = useState(null)
  const [uploadedImage, setUploadedImage] = useState(null)
  const [gender, setGender] = useState('auto')

  const { user, credits, useCredit, canUseFreeCredit } = useAuthStore()
  const {
    startGeneration,
    isGenerating,
    uploadProgress,
    generationProgress,
    currentGeneration,
    error,
    clearError
  } = useGenerationStore()

  const styleCategories = [
    {
      id: 'professional',
      name: 'Professional',
      icon: '💼',
      description: 'Perfect for LinkedIn, resumes, and corporate profiles',
      styles: [
        { id: 'professional-corporate', name: 'Corporate', description: 'Classic corporate headshot' },
        { id: 'professional-executive', name: 'Executive', description: 'Executive-style portrait' },
        { id: 'professional-modern', name: 'Modern Business', description: 'Modern business look' },
        { id: 'professional-outdoor', name: 'Outdoor Professional', description: 'Professional outdoor setting' },
        { id: 'professional-library', name: 'Library Setting', description: 'Sophisticated library backdrop' }
      ]
    },
    {
      id: 'casual',
      name: 'Casual & Lifestyle',
      icon: '🌿',
      description: 'Natural, approachable photos for personal branding',
      styles: [
        { id: 'casual-outdoor', name: 'Golden Hour', description: 'Natural outdoor with golden lighting' },
        { id: 'casual-coffee', name: 'Coffee Shop', description: 'Warm coffee shop atmosphere' },
        { id: 'casual-beach', name: 'Beach Lifestyle', description: 'Relaxed beach vibes' },
        { id: 'casual-park', name: 'Park Setting', description: 'Fresh park with greenery' }
      ]
    },
    {
      id: 'dating',
      name: 'Dating & Social',
      icon: '💖',
      description: 'Attractive photos for dating apps and social media',
      styles: [
        { id: 'dating-romantic', name: 'Romantic', description: 'Soft romantic appeal' },
        { id: 'dating-fun', name: 'Fun & Playful', description: 'Joyful and vibrant' },
        { id: 'dating-elegant', name: 'Elegant', description: 'Sophisticated and classy' },
        { id: 'dating-adventure', name: 'Adventurous', description: 'Active lifestyle vibe' }
      ]
    },
    {
      id: 'creative',
      name: 'Creative & Branding',
      icon: '🎨',
      description: 'Artistic shots for creatives and entrepreneurs',
      styles: [
        { id: 'creative-artistic', name: 'Artistic', description: 'Dramatic artistic flair' },
        { id: 'creative-urban', name: 'Urban Edge', description: 'Modern city backdrop' },
        { id: 'creative-studio', name: 'Studio Pro', description: 'Professional studio setup' },
        { id: 'creative-vintage', name: 'Vintage Film', description: 'Retro film aesthetic' },
        { id: 'creative-minimalist', name: 'Minimalist', description: 'Clean simple design' }
      ]
    },
    {
      id: 'actor',
      name: 'Actor & Model',
      icon: '🎭',
      description: 'Professional headshots for casting and portfolios',
      styles: [
        { id: 'actor-commercial', name: 'Commercial', description: 'Casting-ready headshot' },
        { id: 'actor-dramatic', name: 'Dramatic', description: 'Theatrical intensity' },
        { id: 'model-fashion', name: 'Fashion Model', description: 'High-end editorial' },
        { id: 'model-commercial', name: 'Commercial Model', description: 'Advertising ready' }
      ]
    }
  ]

  const handleImageUpload = (e) => {
    const file = e.target.files[0]
    if (file) {
      if (!isValidImageFile(file)) {
        toast.error('Please upload a valid image file (JPEG, PNG, WebP) under 10MB')
        return
      }

      setUploadedFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setUploadedImage(e.target.result)
      }
      reader.readAsDataURL(file)
      clearError()
    }
  }

  const handleGenerate = async () => {
    if (!uploadedFile || !selectedStyle) {
      toast.error('Please upload an image and select a style')
      return
    }

    if (!user) {
      toast.error('Please sign in to generate headshots')
      return
    }

    // Check if user can use free credit or has paid credits
    const freeCredits = canUseFreeCredit()
    const canGenerate = freeCredits.canUse || credits.remaining > 0

    if (!canGenerate) {
      toast.error('No credits available. Please upgrade your plan to continue creating headshots!')
      return
    }

    // Use credit
    const isFree = freeCredits.canUse && credits.remaining === 0
    const creditResult = await useCredit(isFree)

    if (!creditResult.success) {
      if (creditResult.requiresUpgrade) {
        toast.error(creditResult.error, {
          duration: 6000,
          action: {
            label: 'Upgrade Now',
            onClick: () => window.location.href = '/pricing'
          }
        })
      } else {
        toast.error(creditResult.error)
      }
      return
    }

    // Start generation
    const result = await startGeneration(uploadedFile, selectedStyle, user.id, gender)

    if (result.success) {
      toast.success('Headshot generated successfully!')
    } else {
      toast.error(result.error || 'Generation failed')
    }
  }

  return (
    <div className="min-h-screen gradient-bg py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-secondary-900 mb-4">
            Generate Your Perfect Headshot
          </h1>
          <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
            Upload your photo and choose from our professional styles to create stunning headshots in minutes.
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          {/* Step 1: Upload Image */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="card mb-8"
          >
            <h2 className="text-2xl font-semibold text-secondary-900 mb-6">
              Step 1: Upload Your Photo
            </h2>
            
            {!uploadedImage ? (
              <div className="border-2 border-dashed border-secondary-300 rounded-lg p-12 text-center hover:border-primary-400 transition-colors duration-200">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                  disabled={isGenerating}
                />
                <label htmlFor="image-upload" className={`cursor-pointer ${isGenerating ? 'opacity-50 cursor-not-allowed' : ''}`}>
                  <Upload className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-secondary-900 mb-2">
                    Click to upload your photo
                  </h3>
                  <p className="text-secondary-600 mb-4">
                    Or drag and drop your image here (Max 10MB)
                  </p>
                  <div className="btn-primary inline-flex items-center gap-2">
                    <Upload className="w-4 h-4" />
                    Choose File
                  </div>
                </label>
              </div>
            ) : (
              <div className="flex items-center gap-6">
                <img
                  src={uploadedImage}
                  alt="Uploaded"
                  className="w-32 h-32 object-cover rounded-lg"
                />
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-secondary-900 mb-2">
                    Photo uploaded successfully!
                  </h3>
                  <p className="text-secondary-600 mb-2">
                    {uploadedFile && `${uploadedFile.name} (${formatFileSize(uploadedFile.size)})`}
                  </p>
                  <p className="text-secondary-600 mb-4">
                    Now choose your preferred style and gender below.
                  </p>
                  <div className="flex gap-4">
                    <button
                      onClick={() => {
                        setUploadedImage(null)
                        setUploadedFile(null)
                      }}
                      className="btn-secondary"
                      disabled={isGenerating}
                    >
                      Upload Different Photo
                    </button>

                    {/* Gender Selection */}
                    <div className="flex items-center gap-2">
                      <label className="text-sm font-medium text-secondary-700">Gender:</label>
                      <select
                        value={gender}
                        onChange={(e) => setGender(e.target.value)}
                        className="input py-1 px-2 text-sm"
                        disabled={isGenerating}
                      >
                        <option value="auto">Auto-detect</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Step 2: Choose Style */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="card mb-8"
          >
            <h2 className="text-2xl font-semibold text-secondary-900 mb-6">
              Step 2: Choose Your Style
            </h2>

            <div className="space-y-8">
              {styleCategories.map((category) => (
                <div key={category.id}>
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-2xl">{category.icon}</span>
                    <div>
                      <h3 className="text-lg font-semibold text-secondary-900">
                        {category.name}
                      </h3>
                      <p className="text-secondary-600">{category.description}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {category.styles.map((style) => (
                      <button
                        key={style.id}
                        onClick={() => setSelectedStyle(style.id)}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                          selectedStyle === style.id
                            ? 'border-primary-500 bg-primary-50'
                            : 'border-secondary-200 hover:border-secondary-300'
                        }`}
                      >
                        <div className="aspect-square bg-secondary-100 rounded-lg mb-3 flex items-center justify-center">
                          <Camera className="w-8 h-8 text-secondary-400" />
                        </div>
                        <h4 className="font-medium text-secondary-900 text-sm mb-1">
                          {style.name}
                        </h4>
                        <p className="text-xs text-secondary-600">
                          {style.description}
                        </p>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="card bg-red-50 border-red-200 mb-8"
            >
              <div className="flex items-center gap-3">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <div>
                  <h3 className="font-medium text-red-900">Generation Failed</h3>
                  <p className="text-red-700">{error}</p>
                </div>
                <button
                  onClick={clearError}
                  className="ml-auto text-red-600 hover:text-red-800"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}

          {/* Generation Progress */}
          {isGenerating && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="card mb-8"
            >
              <h3 className="text-xl font-semibold text-secondary-900 mb-6">
                Generating Your Headshot...
              </h3>

              <div className="space-y-4">
                {/* Upload Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Uploading image...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-secondary-200 rounded-full h-2">
                    <div
                      className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Generation Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Generating headshot...</span>
                    <span>{generationProgress}%</span>
                  </div>
                  <div className="w-full bg-secondary-200 rounded-full h-2">
                    <div
                      className="bg-accent-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${generationProgress}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <p className="text-secondary-600 mt-4 text-center">
                This usually takes 30-60 seconds. Please don't close this page.
              </p>
            </motion.div>
          )}

          {/* Generation Result */}
          {currentGeneration && !isGenerating && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="card mb-8"
            >
              <div className="flex items-center gap-3 mb-6">
                <CheckCircle className="w-6 h-6 text-green-600" />
                <h3 className="text-xl font-semibold text-secondary-900">
                  Headshot Generated Successfully!
                </h3>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Original</h4>
                  <img
                    src={currentGeneration.inputImageUrl}
                    alt="Original"
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                </div>
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Generated ({currentGeneration.styleName})</h4>
                  <img
                    src={currentGeneration.outputImageUrl}
                    alt="Generated"
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                </div>
              </div>

              <div className="flex gap-4 mt-6">
                <a
                  href={currentGeneration.outputImageUrl}
                  download={`headshot-${currentGeneration.id}.png`}
                  className="btn-primary inline-flex items-center gap-2"
                >
                  <ArrowRight className="w-4 h-4" />
                  Download High-Res
                </a>
                <button
                  onClick={() => {
                    setUploadedImage(null)
                    setUploadedFile(null)
                    setSelectedStyle(null)
                  }}
                  className="btn-secondary"
                >
                  Generate Another
                </button>
              </div>
            </motion.div>
          )}

          {/* Generate Button */}
          {uploadedImage && selectedStyle && !isGenerating && !currentGeneration && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <button
                onClick={handleGenerate}
                disabled={isGenerating}
                className="btn-primary text-lg px-8 py-4 inline-flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Sparkles className="w-5 h-5" />
                Generate My Headshot
                <ArrowRight className="w-5 h-5" />
              </button>
              <p className="text-secondary-600 mt-4">
                {user ? (
                  canUseFreeCredit().canUse && credits.remaining === 0
                    ? `This will use 1 free credit (${canUseFreeCredit().remaining} of 5 lifetime free remaining)`
                    : `This will use 1 credit (${credits.remaining} remaining)`
                ) : (
                  'Sign in to generate headshots'
                )}
              </p>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Generate
