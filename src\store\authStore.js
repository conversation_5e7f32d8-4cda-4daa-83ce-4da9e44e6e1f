import { create } from 'zustand'
import { auth, db } from '../lib/supabase'

export const useAuthStore = create((set, get) => ({
  // State
  user: null,
  loading: true,
  credits: {
    total: 0,
    used: 0,
    remaining: 0,
    freeUsed: 0,
    lastFreeReset: null,
    tier: 'free'
  },

  // Actions
  initialize: async () => {
    try {
      const { user } = await auth.getCurrentUser()
      if (user) {
        set({ user, loading: false })
        await get().loadUserCredits()
      } else {
        set({ loading: false })
      }
    } catch (error) {
      console.error('Auth initialization error:', error)
      set({ loading: false })
    }
  },

  signIn: async (email, password) => {
    try {
      const { data, error } = await auth.signIn(email, password)
      if (error) throw error
      
      set({ user: data.user })
      await get().loadUserCredits()
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  signUp: async (email, password) => {
    try {
      const { data, error } = await auth.signUp(email, password)
      if (error) throw error
      
      // Initialize user credits for new user
      if (data.user) {
        await get().initializeUserCredits(data.user.id)
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  signInWithGoogle: async () => {
    try {
      const { data, error } = await auth.signInWithGoogle()
      if (error) throw error
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  signOut: async () => {
    try {
      await auth.signOut()
      set({ 
        user: null, 
        credits: {
          total: 0,
          used: 0,
          remaining: 0,
          freeUsed: 0,
          lastFreeReset: null,
          tier: 'free'
        }
      })
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  },

  loadUserCredits: async () => {
    const { user } = get()
    if (!user) return

    try {
      const { data, error } = await db.getUserCredits(user.id)
      if (error && error.code !== 'PGRST116') { // Not found error
        throw error
      }

      if (data) {
        const remaining = data.total_credits - data.used_credits
        set({
          credits: {
            total: data.total_credits,
            used: data.used_credits,
            remaining: Math.max(0, remaining),
            freeUsed: data.free_credits_used,
            lastFreeReset: data.last_free_reset,
            tier: data.subscription_tier
          }
        })
      } else {
        // Initialize credits for existing user
        await get().initializeUserCredits(user.id)
      }
    } catch (error) {
      console.error('Error loading user credits:', error)
    }
  },

  initializeUserCredits: async (userId) => {
    try {
      // New users get 5 free credits to start
      const initialCredits = {
        total: 5,
        used: 0,
        freeUsed: 0,
        lastReset: new Date().toISOString(),
        tier: 'free'
      }

      await db.updateUserCredits(userId, initialCredits)

      set({
        credits: {
          total: 5,
          used: 0,
          remaining: 5,
          freeUsed: 0,
          lastFreeReset: initialCredits.lastReset,
          tier: 'free'
        }
      })
    } catch (error) {
      console.error('Error initializing user credits:', error)
    }
  },

  addCredits: async (amount, tier = null) => {
    const { user, credits } = get()
    if (!user) return

    try {
      const newCredits = {
        total: credits.total + amount,
        used: credits.used,
        freeUsed: credits.freeUsed,
        lastReset: credits.lastFreeReset,
        tier: tier || credits.tier
      }

      await db.updateUserCredits(user.id, newCredits)
      
      set({
        credits: {
          ...credits,
          total: newCredits.total,
          remaining: newCredits.total - newCredits.used,
          tier: newCredits.tier
        }
      })

      return { success: true }
    } catch (error) {
      console.error('Error adding credits:', error)
      return { success: false, error: error.message }
    }
  },

  useCredit: async (isFree = false) => {
    const { user, credits } = get()
    if (!user) return { success: false, error: 'Not authenticated' }

    // Check if user has credits
    if (isFree) {
      // Check lifetime free limit (5 total, no resets)
      if (credits.freeUsed >= 5) {
        return {
          success: false,
          error: 'You\'ve used all 5 free headshots! Upgrade to Pro to continue creating amazing headshots.',
          requiresUpgrade: true
        }
      }
    } else {
      if (credits.remaining <= 0) {
        return { success: false, error: 'No credits remaining. Purchase more to continue.' }
      }
    }

    try {
      const newCredits = {
        total: credits.total,
        used: isFree ? credits.used : credits.used + 1,
        freeUsed: isFree ? credits.freeUsed + 1 : credits.freeUsed,
        lastReset: credits.lastFreeReset,
        tier: credits.tier
      }

      await db.updateUserCredits(user.id, newCredits)
      
      set({
        credits: {
          ...credits,
          used: newCredits.used,
          freeUsed: newCredits.freeUsed,
          remaining: newCredits.total - newCredits.used
        }
      })

      return { success: true }
    } catch (error) {
      console.error('Error using credit:', error)
      return { success: false, error: error.message }
    }
  },

  canUseFreeCredit: () => {
    const { credits } = get()

    // Simple lifetime limit check (no resets)
    const remaining = Math.max(0, 5 - credits.freeUsed)
    return {
      canUse: remaining > 0,
      remaining,
      isLifetimeLimit: true // Flag to indicate this is a lifetime limit
    }
  }
}))

// Initialize auth state
auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_IN' && session?.user) {
    useAuthStore.getState().loadUserCredits()
  } else if (event === 'SIGNED_OUT') {
    useAuthStore.setState({ 
      user: null, 
      credits: {
        total: 0,
        used: 0,
        remaining: 0,
        freeUsed: 0,
        lastFreeReset: null,
        tier: 'free'
      }
    })
  }
})
